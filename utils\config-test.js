/**
 * 配置测试脚本
 * 用于验证环境配置是否正确
 */

import {
  baseUrl,
  baseUrl_common,
  baseUrl_avt,
  wwwUrl,
  assetsUrl,
  geoUrl,
  enableIpValidation,
  enableDebugMode,
  ENVIRONMENT,
  CONFIG,
  APP_CONFIG,
  APP_GCASH,
  APP_LAUNCH,
  APP_VERSION,
  APP_DEVICE,
  APP_DEFAULT_DEVICE_ID,
} from "./config.js";

/**
 * 测试配置导出
 */
export const testConfig = () => {
  console.log("=== 环境配置测试 ===");
  console.log("当前环境:", ENVIRONMENT);
  console.log("baseUrl:", baseUrl);
  console.log("baseUrl_common:", baseUrl_common);
  console.log("baseUrl_avt:", baseUrl_avt);
  console.log("wwwUrl:", wwwUrl);
  console.log("assetsUrl:", assetsUrl);
  console.log("geoUrl:", geoUrl);
  console.log("enableIpValidation:", enableIpValidation);
  console.log("enableDebugMode:", enableDebugMode);

  // 验证所有配置都不为空
  const configs = { baseUrl, baseUrl_common, baseUrl_avt, wwwUrl, assetsUrl, geoUrl, enableIpValidation, enableDebugMode };
  const emptyConfigs = Object.entries(configs).filter(([key, value]) => !value);

  if (emptyConfigs.length > 0) {
    console.error("❌ 发现空配置:", emptyConfigs);
    return false;
  }

  console.log("✅ 所有配置验证通过");
  return true;
};

/**
 * 测试所有环境配置
 */
export const testAllEnvironments = () => {
  console.log("=== 所有环境配置测试 ===");

  Object.entries(CONFIG).forEach(([env, config]) => {
    console.log(`\n--- ${env.toUpperCase()} 环境 ---`);
    Object.entries(config).forEach(([key, value]) => {
      console.log(`${key}: ${value}`);
    });
  });
};

/**
 * 验证 URL 格式
 */
export const validateUrls = () => {
  console.log("=== URL 格式验证 ===");

  const configs = { baseUrl, baseUrl_common, baseUrl_avt, wwwUrl, assetsUrl, geoUrl };
  let allValid = true;

  Object.entries(configs).forEach(([key, url]) => {
    try {
      new URL(url);
      console.log(`✅ ${key}: ${url}`);
    } catch (error) {
      console.error(`❌ ${key}: ${url} - 无效的 URL 格式`);
      allValid = false;
    }
  });

  return allValid;
};

/**
 * 测试应用固定配置
 */
export const testAppConfig = () => {
  console.log("=== 应用固定配置测试 ===");

  // 测试 GCASH 配置
  console.log("GCASH 配置:");
  console.log(`  packageName: ${APP_GCASH.packageName}`);
  console.log(`  appId: ${APP_GCASH.appId}`);
  console.log(`  channel: ${APP_GCASH.channel}`);
  console.log(`  telephoneCode: ${APP_GCASH.telephoneCode}`);

  // 测试版本配置
  console.log("版本配置:");
  console.log(`  统一版本: ${APP_VERSION}`);

  // 测试设备配置
  console.log("设备配置:");
  console.log(`  terminal: ${APP_DEVICE.terminal}`);
  console.log(`  model: ${APP_DEVICE.model}`);
  console.log(`  source: ${APP_DEVICE.source}`);

  // 验证必要字段不为空
  const requiredFields = [
    ["APP_GCASH.packageName", APP_GCASH.packageName],
    ["APP_GCASH.appId", APP_GCASH.appId],
    ["APP_VERSION", APP_VERSION],
    ["APP_DEVICE.terminal", APP_DEVICE.terminal],
    ["APP_DEFAULT_DEVICE_ID", APP_DEFAULT_DEVICE_ID],
  ];

  const emptyFields = requiredFields.filter(([name, value]) => !value);

  if (emptyFields.length > 0) {
    console.error(
      "❌ 发现空的应用配置:",
      emptyFields.map(([name]) => name)
    );
    return false;
  }

  console.log("✅ 所有应用配置验证通过");
  return true;
};

// 如果直接运行此文件，执行所有测试
if (typeof window === "undefined" && typeof global !== "undefined") {
  // Node.js 环境
  testConfig();
  testAllEnvironments();
  validateUrls();
  testAppConfig();
}
