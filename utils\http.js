import { baseUrl, baseUrl_common, baseUrl_avt, wwwUrl, assetsUrl, APP_DEVICE, APP_GCASH, APP_VERSION } from "./config.js";

/**
 * HTTP 请求工具类
 * 提供统一的请求方法，支持多种 baseUrl 和通用配置
 */
class HttpClient {
  constructor() {
    // 默认配置
    this.defaultConfig = {
      method: "POST",
      dataType: "json",
      headers: {
        terminal: APP_DEVICE.terminal,
      },
    };
  }

  /**
   * 通用请求方法
   * @param {Object} options - 请求配置
   * @param {string} options.url - 请求URL（相对路径或完整URL）
   * @param {string} [options.baseUrl] - 基础URL类型：'common'|'avt'|'main'|'geo'|'custom'
   * @param {string} [options.method='POST'] - 请求方法
   * @param {Object} [options.data={}] - 请求数据
   * @param {Object} [options.headers={}] - 请求头
   * @param {string} [options.dataType='json'] - 数据类型
   * @param {string} [options.token] - 用户token，会自动添加到data和headers中
   * @param {boolean} [options.tokenInHeader=false] - 是否将token添加到请求头中
   * @param {boolean} [options.tokenInData=true] - 是否将token添加到请求数据中
   * @param {string} [options.tokenHeaderName='Token'] - token在请求头中的字段名
   * @param {boolean} [options.showLoading=false] - 是否显示加载提示
   * @param {boolean} [options.hideLoadingOnComplete=true] - 请求完成后是否自动隐藏加载提示
   * @returns {Promise} 请求Promise
   */
  request(options = {}) {
    const {
      url,
      baseUrl: baseUrlType = "common",
      method = this.defaultConfig.method,
      data = {},
      headers = {},
      dataType = this.defaultConfig.dataType,
      token,
      tokenInHeader = true,
      tokenInData = true,
      tokenHeaderName = "Token",
      showLoading = false,
      hideLoadingOnComplete = true,
    } = options;

    // 构建完整URL
    const fullUrl = this._buildUrl(url, baseUrlType);

    // 构建请求数据
    const requestData = this._buildRequestData(data, token, tokenInData);

    // 构建请求头
    const requestHeaders = this._buildRequestHeaders(headers, token, tokenInHeader, tokenHeaderName);

    // 记录是否显示了loading，确保一定会隐藏
    let loadingShown = false;

    // 显示加载提示
    if (showLoading) {
      my.showLoading();
      loadingShown = true;
    }

    // 安全的隐藏loading函数
    const safeHideLoading = () => {
      if (loadingShown && hideLoadingOnComplete) {
        try {
          my.hideLoading();
        } catch (error) {
          console.warn("Failed to hide loading:", error);
        }
        loadingShown = false;
      }
    };

    return new Promise((resolve, reject) => {
      my.request({
        url: fullUrl,
        method,
        data: requestData,
        headers: requestHeaders,
        dataType,
        success: (res) => {
          safeHideLoading();
          resolve(res);
        },
        fail: (err) => {
          safeHideLoading();
          reject(err);
        },
      });
    });
  }

  /**
   * 构建完整URL
   * @private
   */
  _buildUrl(url, baseUrlType) {
    // 如果是完整URL，直接返回
    if (url.startsWith("http://") || url.startsWith("https://")) {
      return url;
    }

    // 根据baseUrlType选择对应的baseUrl
    const baseUrlMap = {
      main: baseUrl,
      common: baseUrl_common,
      avt: baseUrl_avt,
      www: wwwUrl,
      assets: assetsUrl,
    };

    const selectedBaseUrl = baseUrlMap[baseUrlType] || baseUrl_common;

    // 确保URL拼接正确
    const cleanUrl = url.startsWith("/") ? url.slice(1) : url;
    const cleanBaseUrl = selectedBaseUrl.endsWith("/") ? selectedBaseUrl : selectedBaseUrl + "/";

    return cleanBaseUrl + cleanUrl;
  }

  /**
   * 构建请求数据
   * @private
   */
  _buildRequestData(data, token, tokenInData = true) {
    const requestData = { ...data };

    // 自动添加token到请求数据中
    if (token && tokenInData) {
      requestData.token = token;
    }

    return requestData;
  }

  /**
   * 构建请求头
   * @private
   */
  _buildRequestHeaders(headers, token, tokenInHeader = false, tokenHeaderName = "Token") {
    const requestHeaders = {
      ...this.defaultConfig.headers,
      ...headers,
    };

    // 自动添加token到请求头中
    if (token && tokenInHeader) {
      requestHeaders[tokenHeaderName] = token;
    }

    return requestHeaders;
  }

  /**
   * 快捷方法：GET请求
   */
  get(url, options = {}) {
    return this.request({
      ...options,
      url,
      method: "GET",
    });
  }

  /**
   * 快捷方法：POST请求
   */
  post(url, data = {}, options = {}) {
    return this.request({
      ...options,
      url,
      method: "POST",
      data,
    });
  }

  /**
   * 快捷方法：带token的请求（token在data中）
   */
  requestWithToken(url, token, data = {}, options = {}) {
    return this.request({
      ...options,
      url,
      data,
      token,
      tokenInData: true,
      tokenInHeader: false,
    });
  }

  /**
   * 快捷方法：带token的请求（token在header中）
   */
  requestWithTokenHeader(url, token, data = {}, options = {}) {
    return this.request({
      ...options,
      url,
      data,
      token,
      tokenInData: false,
      tokenInHeader: true,
    });
  }

  /**
   * 快捷方法：带token的请求（token同时在data和header中）
   */
  requestWithTokenBoth(url, token, data = {}, options = {}) {
    return this.request({
      ...options,
      url,
      data,
      token,
      tokenInData: true,
      tokenInHeader: true,
    });
  }

  /**
   * 快捷方法：带加载提示的请求
   */
  requestWithLoading(url, data = {}, options = {}) {
    return this.request({
      ...options,
      url,
      data,
      showLoading: true,
    });
  }
}

// 创建单例实例
const httpClient = new HttpClient();

/**
 * 兼容原有的request方法
 * @deprecated 建议使用新的httpClient实例方法
 */
export const request = (url, data = {}, mode = "POST", headers = {}) => {
  return httpClient.request({
    url,
    data,
    method: mode,
    headers,
    baseUrl: "main", // 保持原有行为
  });
};

// 导出新的HTTP客户端实例和方法
export { httpClient };
export const { get, post, requestWithToken, requestWithTokenHeader, requestWithTokenBoth, requestWithLoading } = httpClient;

// 导出配置常量
export { baseUrl, baseUrl_common, baseUrl_avt, wwwUrl, assetsUrl };
