<view class="launch-page">
  <view class="launch-container">
    <view class="launch-logo">
      <image mode="widthFix" src="../../assets/new-icons/launchlogo.png" />
    </view>
    <view class="launch-nav">
      <image mode="widthFix" src="../../assets/new-icons/launch_nav.png" />
    </view>
    <view class="loading-box">
      <view class="loading-progress">
        <view class="loading-progress-bar" style="width: {{progress}}%"></view>
      </view>
      <view class="loading-font">
        Loading...
      </view>
    </view>
    <view class="launch-version">v{{app_version}}</view>
  </view>
</view>

<!-- 系统维护 有按钮 -->
<view class="mask-one flexcenter {{showMessageConfrim?'flex':'hiddenno'}}">
  <view class="messageConfrim">
    <view class="confrim-title">
      <view style="width: 48rpx;height:1rpx;"></view>
      <view>{{showMessageTitle}}</view>
      <image onTap="offConfrimMask" mode="scaleToFill" src="../../assets/new-icons/close.png" />
    </view>
    <view class="confrim-param">
      {{showMessagePagrm}}
    </view>
    <button
      a:if={{showMessageTitle == 'System Maintenance'}}
      onTap="exitMiniProgram"
      class="confrim-done"
    >
      Exit
    </button>
    <view a:else onTap="confrimMask" class="confrim-done">Contact Support</view>
  </view>
</view>

<!-- IP限制 无按钮  -->
<view class="flex-zindex {{ ip_restricted ?'flex':'hiddenno'}}">
  <view class="mask-one flexcenter {{ ip_restricted ?'flex':'hiddenno'}}">
    <view class="messageConfrim messageConfrim_ipset">
      <view class="confrim-title dialog-title-main">
        <view>Item Not Available</view>
      </view>
      <view class="dialog-title-top small-font">
        Sorry，This service isn't availablein your regin yet.
        <view>We apologize for the inconvenience caused to you.</view>
      </view>
    </view>
  </view>
</view>