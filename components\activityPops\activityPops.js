import PopupManager from './PopupManager'

Component({
  mixins: [],
  data: {
    // 当前展示的弹窗信息
    currentPopup: null,
    // 弹窗类型映射表，控制不同类型弹窗的显示/隐藏
    popupVisible: {
      Tip21Old: false,
      VipTip: false,
      VipCashback: false,
      ActivityBonus: false,
      RegisterBonus: false,
      BonusPopups: false,
    },
    resetVisivle: {
      Tip21Old: false,
      VipTip: false,
      VipCashback: false,
      ActivityBonus: false,
      RegisterBonus: false,
      BonusPopups: false,
    },
    // 未满21岁禁止访问弹框
    forbid_access: false,

    // 活动数据
    activityPops: [],
    // 跳转链接弹框
    showJumpConfrim_1: false,
    jumpTypeInfo: {
      type: 'game',
      id: '123'
    },
    depositAward: ''
  },

  props: {
    token: '',
    // 首充奖励 特殊判断
    deposit_award: '',
    info: {
      token: '',
      isVip: false,
      register_award: {},
      is_register: 0
    },
    // 首冲奖励关闭回调
    onCloseDepositPopup: Function,
    // 关闭调账奖励弹窗后回调更新余额
    onUpdateAvailableCoins: Function
  },

  didMount() {
    // 创建弹窗管理器
    console.log('this.props.info', this.props.info);
    if (this.props.info.token) {
      this.popupManager = new PopupManager(this.props.info);
      this.popupManager.setPopupChangeCallback(this.handlePopupChange.bind(this));
      this.popupManager.initialize();
    }

    this.setData({
      token: this.props.info.token,
      depositAward: this.props.deposit_award
    })
  },

  didUpdate(prevProps) {
    console.log('组件更新', this.props.info);
    // console.log('prevProps', prevProps.info);
    // token变化时重新初始化
    if (prevProps.info.token !== this.props.info.token && this.props.info.token || prevProps.info.awardTypes !== this.props.info.awardTypes) {
      this.popupManager = new PopupManager(this.props.info);
      this.popupManager.setPopupChangeCallback(this.handlePopupChange.bind(this));
      this.popupManager.initialize();
    }
    // 首充奖励变化时更新
    if (prevProps.deposit_award !== this.props.deposit_award) {
      this.setData({
        depositAward: this.props.deposit_award
      })
    }
    this.setData({
      token: this.props.info.token,
    })
  },

  methods: {
    // 初始化弹窗可见性
    initPopupVisible() {
      for (let key in this.data.popupVisible) {
        this.data.popupVisible[key] = false;
      }
      // this.setData({
      //   popupVisible: this.data.popupVisible
      // });
    },
    // 打开外部链接（监管条款）
    onOpenParentLink(e) {
      // 打开外部链接（监管条款）
      const url = e.currentTarget.dataset.url
      this.onConfirmOpenLinkPopup({
        data: {
          type: 'parent',
          id: '123',
          url: url
        }
      });
    },

    // 确认首充奖励弹窗，打开跳转
    onConfirmDepositPopup() {
      // 首冲奖励跳转
      this.onConfirmOpenLinkPopup({
        data: {
          type: 'Bet',
          id: '123'
        }
      });
      // 关闭首充奖励弹窗
      this.closeDepositPopup();
    },
    // 关闭首充奖励弹窗
    closeDepositPopup() {
      this.props.onCloseDepositPopup();
    },
    // 处理弹窗变化
    handlePopupChange(popup) {
      console.log('弹窗变化:', popup);
      const popupVisible = {
        Tip21Old: false,
        VipTip: false,
        VipCashback: false,
        ActivityBonus: false,
        RegisterBonus: false
      };

      // 设置当前弹窗可见
      if (popup) {
        popupVisible[popup.id] = true;
      }
      // 更新数据
      this.setData({
        popupVisible: popupVisible,
        currentPopup: popup
      });
      // 更新余额
      if (!popup) {
        // 余额刷新延迟，+定时器
        setTimeout(() => {
          this.props.onUpdateAvailableCoins();
        }, 1000)
      }
    },

    // 拒绝条款（21岁验证）
    onReject21Terms() {
      // this.popupManager.closeCurrentPopup({ agree: false });
      this.openForbidAccess();
    },
    // 确认关闭按钮
    onConfirmClosePopup() {
      this.popupManager.closeCurrentPopup({
        type: 'close'
      });
    },
    // 确认跳转按钮 --go bet
    onConfirmOpenPopup() {
      this.onConfirmOpenLinkPopup(this.data.currentPopup);
    },
    // 确认领取按钮
    onConfirmClaimPopup() {
      this.popupManager.closeCurrentPopup({
        type: 'claim'
      });
    },
    // 处理跳转按钮
    onConfirmOpenLinkPopup(popup) {
      this.setData({
        showJumpConfrim_1: true,
        jumpTypeInfo: {
          type: popup.data.type,
          id: popup.data.id
        }
      })
      this.popupManager.closeCurrentPopup({
        type: 'close'
      });
    },
    // 关闭跳转链接弹框
    offOpenLinkDialog() {
      this.setData({
        showJumpConfrim_1: false
      })
    },
    // 关闭弹窗（只适用于允许关闭的弹窗）
    onClosePopup() {
      return
      const currentPopup = this.data.currentPopup;
      if (currentPopup && currentPopup.allowClose) {
        this.popupManager.closeCurrentPopup({
          closed: true
        });
      }
    },

    // 打开未满21岁禁止访问弹框
    openForbidAccess() {
      // this.initPopupVisible();
      this.setData({
        forbid_access: true,
      });
    },

    // 未满21岁禁止访问弹框关闭
    forbidAccessDone() {
      this.setData({
        forbid_access: false
      });
    },

    // 点击弹窗外部区域
    onTapOverlay() {
      const currentPopup = this.data.currentPopup;
      if (currentPopup && currentPopup.allowClose) {
        this.onClosePopup();
      }
    },

    // 打开条款弹窗
    openTerms() {
      my.navigateTo({
        url: '/pages/terms/terms'
      });
    }
  }
});