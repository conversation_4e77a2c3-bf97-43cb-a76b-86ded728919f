/**
 * 应用配置文件
 * 统一管理所有环境的 URL 配置和应用固定参数
 * 上线前只需要修改 ENVIRONMENT 变量即可切换环境
 */

// 环境配置：'dev' | 'prod'   局限于GcashAPP,只有预发与线上环境
// 或者终端运行命令： node scripts/switch-env.js prod
const ENVIRONMENT = 'dev';

// 应用固定配置参数（不随环境变化）
const APP_CONFIG = {
  // 应用包名配置
  gcash: {
    packageName: "com.nustargame.gcash",
    appId: "2170020216334562",
    channel: "Gcash",
    telephoneCode: "+63",
    registrationChannel: "Gcash",
  },

  // 启动页面应用配置
  launch: {
    packageName: "com.playmate.playzone",
    channel: "Gcash",
    source: "mania",
  },

  // 应用版本号 - 统一版本
  appVersion: "1.0.184",

  // 设备相关配置
  device: {
    model: "WEB",
    version: "WEB",
    language: "null",
    timezone: "null",
    source: "1",
    isNative: "0",
    terminal: 8,
  },

  // 默认设备ID后缀
  defaultDeviceId: "gcash2rmv3iln5qq0",
};

// 各环境配置
const CONFIG = {
  // 开发环境（预发）
  dev: {
    baseUrl: "https://pre.nustaronline.vip/",
    baseUrl_common: "https://pre.nustaronline.vip/common/",
    baseUrl_avt: "https://pre.nustaronline.vip/avt/",
    wwwUrl: "https://gcash.nustaronline.vip/",
    assetsUrl: "https://uat-nustar-static.nustaronline.vip/",
    geoUrl: "https://pre-geo.nustaronline.vip:4433/geo",
    // 功能开关配置
    enableIpValidation: false, // 开发环境关闭 IP 验证
    enableDebugMode: false, // 开发环境是否开启调试模式 是否首冲
    debugAuthCode: "20250804SjiYjNrmarM0885600342584", // authCode 本地开发测试用
    doGetAuthCode: false, // 是否需要去手动获取authcode
  },

  // 生产环境
  prod: {
    baseUrl: "https://io.nustargame.com/",
    baseUrl_common: "https://io.nustargame.com/common/",
    baseUrl_avt: "https://io.nustargame.com/avt/",
    wwwUrl: "https://gcash.nustargame.com/",
    assetsUrl: "https://nustar-static.nustargame.com/",
    geoUrl: "https://geo.nustargame.com",
    // 功能开关配置
    enableIpValidation: true, // 生产环境开启 IP 验证
    enableDebugMode: false, // 生产环境关闭调试模式
    debugAuthCode: "", // 生产环境实时获取code
    doGetAuthCode: false,
  },
};

// 获取当前环境配置
const getConfig = () => {
  const config = CONFIG[ENVIRONMENT];
  if (!config) {
    console.error(`Invalid environment: ${ENVIRONMENT}`);
    return CONFIG.dev; // 默认返回开发环境配置
  }
  return config;
};

// 导出当前环境的配置
const currentConfig = getConfig();

export const {
  baseUrl,
  baseUrl_common,
  baseUrl_avt,
  wwwUrl,
  assetsUrl,
  geoUrl,
  enableIpValidation,
  enableDebugMode,
  debugAuthCode,
  doGetAuthCode
} = currentConfig;

// 导出应用固定配置
export const {
  gcash: APP_GCASH,
  launch: APP_LAUNCH,
  appVersion: APP_VERSION,
  device: APP_DEVICE,
  defaultDeviceId: APP_DEFAULT_DEVICE_ID
} = APP_CONFIG;

// 导出环境标识和完整配置对象
export {
  ENVIRONMENT,
  CONFIG,
  APP_CONFIG,
  getConfig
};

// 默认导出当前配置
export default currentConfig;