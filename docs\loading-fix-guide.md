# Loading 状态管理修复指南

## 问题描述

在项目中发现了 `my.hideLoading()` 未执行的问题，导致加载提示一直显示，影响用户体验。

## 问题原因分析

### 1. 条件判断问题
在 `utils/http.js` 中，`my.hideLoading()` 的执行依赖于多个条件：
```javascript
if (hideLoadingOnComplete && showLoading) {
  my.hideLoading();
}
```
如果任一条件为 `false`，都不会执行 `hideLoading`。

### 2. 异常处理不完善
当请求发生异常时，可能跳过了 `hideLoading` 的执行。

### 3. 异步操作中的状态管理
在复杂的异步操作中，loading 状态可能被遗忘或重复调用。

## 解决方案

### 1. 改进的 HTTP 工具类

已更新 `utils/http.js`，增加了：
- 状态跟踪：记录是否显示了 loading
- 安全隐藏：使用 try-catch 包装 hideLoading 调用
- 状态重置：确保状态正确更新

### 2. Loading 管理器

新增 `utils/loading-manager.js`，提供：
- 状态管理：跟踪所有 loading 状态
- 超时保护：防止 loading 一直显示
- 安全操作：异常处理和状态恢复
- 唯一标识：支持多个 loading 的管理

### 3. 请求管理器集成

更新 `utils/request-manager.js`，使用新的 loading 管理器。

## 使用方法

### 基础用法

```javascript
import loadingManager, { showLoading, hideLoading } from '/utils/loading-manager.js';

// 显示 loading
const loadingId = showLoading({
  content: "Loading...",
  timeout: 10000 // 10秒超时
});

// 隐藏 loading
hideLoading(loadingId);
```

### 请求包装器

```javascript
import { wrapRequest } from '/utils/loading-manager.js';

// 自动管理 loading 的请求
const result = await wrapRequest(async () => {
  return await my.request({
    url: 'api/test',
    method: 'POST'
  });
}, {
  showLoading: true,
  loadingContent: "请求中...",
  timeout: 15000
});
```

### 在页面中使用

```javascript
import loadingManager from '/utils/loading-manager.js';

Page({
  async getData() {
    const loadingId = loadingManager.show({
      content: "加载数据中..."
    });

    try {
      const response = await apiService.getUserInfo(this.data.token, this);
      // 处理数据
    } catch (error) {
      console.error('获取数据失败:', error);
    } finally {
      // 确保在任何情况下都隐藏 loading
      loadingManager.hide(loadingId);
    }
  }
});
```

## 修复现有代码

### 1. 替换直接调用

**修复前：**
```javascript
my.showLoading();
// ... 异步操作
my.hideLoading();
```

**修复后：**
```javascript
import { showLoading, hideLoading } from '/utils/loading-manager.js';

const loadingId = showLoading();
try {
  // ... 异步操作
} finally {
  hideLoading(loadingId);
}
```

### 2. 修复条件分支

**修复前：**
```javascript
my.showLoading();
if (condition1) {
  // 处理逻辑1
  my.hideLoading();
} else if (condition2) {
  // 处理逻辑2
  // 忘记调用 hideLoading
} else {
  // 处理逻辑3
  my.hideLoading();
}
```

**修复后：**
```javascript
import { wrapRequest } from '/utils/loading-manager.js';

await wrapRequest(async () => {
  if (condition1) {
    // 处理逻辑1
  } else if (condition2) {
    // 处理逻辑2
  } else {
    // 处理逻辑3
  }
}, { showLoading: true });
```

### 3. 修复错误处理

**修复前：**
```javascript
my.showLoading();
my.request({
  url: 'api/test',
  success: (res) => {
    my.hideLoading();
    // 处理成功
  },
  fail: (err) => {
    // 忘记调用 hideLoading
    console.error(err);
  }
});
```

**修复后：**
```javascript
import loadingManager from '/utils/loading-manager.js';

const loadingId = loadingManager.show();
my.request({
  url: 'api/test',
  success: (res) => {
    loadingManager.hide(loadingId);
    // 处理成功
  },
  fail: (err) => {
    loadingManager.hide(loadingId);
    console.error(err);
  }
});
```

## 最佳实践

### 1. 使用 try-finally 确保清理
```javascript
const loadingId = showLoading();
try {
  // 业务逻辑
} finally {
  hideLoading(loadingId);
}
```

### 2. 使用请求包装器
```javascript
// 推荐：自动管理 loading
await wrapRequest(requestFunction, { showLoading: true });
```

### 3. 设置合理的超时时间
```javascript
showLoading({
  content: "Loading...",
  timeout: 10000 // 根据实际需要设置
});
```

### 4. 在页面卸载时清理
```javascript
Page({
  onUnload() {
    // 清理所有 loading
    loadingManager.hideAll();
  }
});
```

## 调试工具

### 检查当前状态
```javascript
console.log('Is loading showing:', loadingManager.isShowing());
console.log('Current loading:', loadingManager.getCurrentLoading());
```

### 强制清理
```javascript
// 紧急情况下强制隐藏所有 loading
loadingManager.hideAll();
```

## 注意事项

1. **向后兼容**：现有的 `my.showLoading()` 和 `my.hideLoading()` 调用仍然有效
2. **渐进迁移**：可以逐步替换现有代码，不需要一次性全部修改
3. **错误处理**：新的管理器包含完善的错误处理，但仍建议在业务代码中添加 try-catch
4. **性能影响**：新的管理器增加了少量开销，但提供了更好的稳定性

## 测试验证

### 1. 基础功能测试
- 显示和隐藏 loading 是否正常
- 超时保护是否生效
- 错误情况下是否正确清理

### 2. 并发测试
- 多个 loading 同时显示的处理
- 快速连续调用的处理

### 3. 异常测试
- 网络错误时的 loading 处理
- 页面跳转时的 loading 清理

通过以上修复方案，可以有效解决 `my.hideLoading()` 未执行的问题，提升应用的稳定性和用户体验。
