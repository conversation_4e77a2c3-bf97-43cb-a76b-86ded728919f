import { getDeviceId, generateMockData } from "/utils/storage-manager";
import { AWARD_NAME_OBJECTS } from "/utils/types";
// import debounce from 'debounce';
import { throttleFirst } from "/utils/tools";
import { baseUrl_common, baseUrl_avt, wwwUrl, assetsUrl, enableDebugMode, debugAuthCode, doGetAuthCode, APP_GCASH, APP_VERSION, APP_DEVICE, APP_DEFAULT_DEVICE_ID } from "/utils/config.js";
import { httpClient } from "/utils/http.js";
import apiService from "/utils/api-service.js";

const MERGE_ITEM_CONSTANT = {
  // 分页、总计金额等信息
  suc_amount: 0,
  total_amount: 0,
  combine_order_no: "--",
  count: 0,
  current_page: 0,
  total_page: 0,
};
Page({
  data: {
    // URL 配置从 utils/config.js 导入
    baseUrl: baseUrl_common,
    baseUrl_avt: baseUrl_avt,
    wwwUrl: wwwUrl,
    assetsUrl: assetsUrl,

    currentId: "",
    token: "",
    gcashAuthCode: "",
    availableCoins: 0,
    showDepositMask: false,
    selectedAmount: 0,
    selectedCoins: 0,
    selectedConfigId: 0,
    showDepositOneMask: false,
    showRedeemMask: false,
    showRedeemOneMask: false,
    account_locked: false,
    showUserInfoMask: false,
    availableCoinsImageClass: "",
    isFocus: false,
    redeemList: [
      {
        name: "aaaa",
        price: "300",
      },
      {
        name: "bb",
        price: "400",
      },
      {
        name: "c",
        price: "500",
      },
    ],
    selectedRedeemName: "",
    selectedRedeemCoins: 0,
    selectedRedeemId: 0,
    selectedRedeemType: 0,
    showPopwindowMask: true,
    showTransactionMask: false,
    // 重构：按接口类别分别存储三个数据来源
    transactionData: {
      // 充值记录 (activePicker: 0)
      topup: {
        list: [], // 原始数据列表
        groupedList: [], // 按日期分组的列表
        pageNum: 1, // 当前页码
        isNoMoreData: false, // 是否没有更多数据
      },
      // 提现记录 (activePicker: 1)
      withdrawal: {
        list: [],
        groupedList: [],
        pageNum: 1,
        isNoMoreData: false,
      },
      // 奖励记录 (activePicker: 2)
      reward: {
        list: [],
        groupedList: [],
        pageNum: 1,
        isNoMoreData: false,
      },
    },
    // 兼容性：保留原有字段，通过计算属性获取当前激活的数据
    transactionHistoryList: [],
    transactionHistoryCount: 0,
    isNoMoreData: false,
    curTransactionPageNum: 1,
    curTransactionType: "charge",
    transactionTypeArray: [
      {
        id: 0,
        name: "Top-Up",
      },
      {
        id: 1,
        name: "Withdrawal",
      },
      {
        id: 2,
        name: "Reward",
      },
    ],
    transactionTypeIndex: 0,
    transactionDateArray: [
      {
        id: 0,
        name: "Today",
      },
      {
        id: 1,
        name: "Yesterday",
      },
      {
        id: 2,
        name: "Last 3 days",
      },
      {
        id: 3,
        name: "Last 7 days",
      },
    ],
    transactionStatusArray: [
      {
        id: 0,
        name: "All",
        checked: false,
      },
      {
        id: 1,
        name: "Successful",
        checked: false,
      },
      {
        id: 2,
        name: "Pending",
      },
      {
        id: 3,
        name: "Unsuccessful",
      },
      // {
      //   id: 7,
      //   name: 'Waiting for Payment'
      // },
    ],
    transactionDateIndex: 0,
    moneyToCoinRatio: 1,
    depositList: [],
    gameList: null,
    marketChannel: "", // 渠道
    navPaddingTop: 100,
    activePicker: 0,
    showSelectTime: false,
    showSelectStatus: false,
    activeStatusText: "All",
    activeTimer: 0,
    activeStatus: [0],
    userInfo: {
      user_id: "-------------------",
      balance: "-------",
      is_vip: 0,
      is_register: 0,
    },
    KYCuserInfo: {},
    gameType: [],
    // 充值提现弹窗临时变量
    selectDepositIndex: null,
    depositValue: "",
    totalDepositValue: "",
    depositCurrencyValue: "",
    showMessageConfrim: false,
    showMessageTitle: "",
    showMessageBtnText: "", // 弹窗按钮显示文本
    showMessagePagrm: "",
    validInputValue: true,
    validInputValueText: "",
    showJumpConfrim_1: false,
    is_21_years_old: false,
    forbid_access_no_button: false,
    ip_restricted: false,
    forbid_access: false,
    jumpType: 0,
    banner_id: "",
    bannerList: [],
    // 充值体现详情
    showDetailMask: false,

    // 备份列表数据
    temTransactionHistoryList: [],
    copyChargeHistoryList: [],
    type_id: "",

    animationData: {}, // 动画数据
    notificationText: "",
    marqueeDuration: "",
    account_locked_msg: "",
    customer_email: "customer_email_xxx",
    // 是否有首充
    is_first_charge: false,
    depositAward: 0,
    retryCount: 0,
    store_authCode: "",
    currentId: "",
    currentBet: 131,
    totalBet: 1000,
    riskDialogRef: null,
    deposit_award: "",
    isLoginloading: false,
    activityPopsInfo: {},
    awardTypes: [],
    topUpBatched: false, //是否分批次充值
    topUpBatchColor: "#ffffff",
    // 分批提现组合订单列表详情
    openMergeOrderId: null,
    withdrawalMergeOrderList: [], // 列表
    withdrawalMergeOrderItem: MERGE_ITEM_CONSTANT, // 分页、总计金额等信息
    handleRefreshIconClick: null, // 存储刷新余额按钮的函数
    animation: null, // 存储动画
    maximum_single_recharge: 0, // 自动设置连续充值的最大值

    // 下载引导浮标配置
    downloadGuideConfig: {
      show: false, // 开发环境默认显示，生产环境根据后台配置
      content: "", // 浮标提示内容
      link: "", // 点击跳转链接
    },
  },

  // 辅助方法：获取当前激活的交易数据类型
  getCurrentTransactionType() {
    const typeMap = {
      0: "topup", // 充值
      1: "withdrawal", // 提现
      2: "reward", // 奖励
    };
    return typeMap[this.data.activePicker] || "topup";
  },

  // 辅助方法：获取当前激活的交易数据
  getCurrentTransactionData() {
    const type = this.getCurrentTransactionType();
    return this.data.transactionData[type];
  },

  // 辅助方法：更新当前激活的交易数据
  updateCurrentTransactionData(updates) {
    const type = this.getCurrentTransactionType();
    const currentData = {
      ...this.data.transactionData[type],
      ...updates,
    };

    this.setData({
      [`transactionData.${type}`]: currentData,
      // 同步更新兼容性字段
      transactionHistoryList: currentData.groupedList,
      isNoMoreData: currentData.isNoMoreData,
      curTransactionPageNum: currentData.pageNum,
    });
  },

  // 辅助方法：重置指定类型的交易数据
  resetTransactionData(type = null) {
    const targetType = type || this.getCurrentTransactionType();
    const resetData = {
      list: [],
      groupedList: [],
      pageNum: 1,
      isNoMoreData: false,
    };

    this.setData({
      [`transactionData.${targetType}`]: resetData,
      // 如果重置的是当前激活的类型，同步更新兼容性字段
      ...(targetType === this.getCurrentTransactionType() && {
        transactionHistoryList: [],
        isNoMoreData: false,
        curTransactionPageNum: 1,
      }),
    });
  },

  // 辅助方法：重置所有交易数据
  resetAllTransactionData() {
    this.setData({
      transactionData: {
        topup: {
          list: [],
          groupedList: [],
          pageNum: 1,
          isNoMoreData: false,
        },
        withdrawal: {
          list: [],
          groupedList: [],
          pageNum: 1,
          isNoMoreData: false,
        },
        reward: {
          list: [],
          groupedList: [],
          pageNum: 1,
          isNoMoreData: false,
        },
      },
      transactionHistoryList: [],
      isNoMoreData: false,
      curTransactionPageNum: 1,
    });
  },

  onLoad(e) {
    // 隐藏Home按钮
    my.hideBackHome();
    console.log("onLoad");
    my.getStorage({
      key: "gameType",
      success: (res) => {
        this.setData({
          gameType: res.data,
        });
      },
    });
    my.getStorage({
      key: "jumpQuery",
      success: (res) => {
        this.setData({
          jumpQuery: res.data,
        });
      },
    });
    my.getStorage({
      key: "currentId",
      success: (res) => {
        this.setData({
          currentId: res.data,
        });
      },
    });

    const systemInfo = my.getSystemInfoSync();
    this.setData({
      navPaddingTop: systemInfo.statusBarHeight + "px",
    });
    my.setNavigationBar({
      backgroundColor: "#FFB701",
    });
    // 获取消息
    this.getNotification();
    my.getStorage({
      key: "token",
      success: (res) => {
        // --debug
        if (res.data) {
          this.setData({
            token: res.data,
          });
          // 有token并且authcode相同则getUserInfo
          // 有token且authcode不相同则getAuthCode
          // authcode不相同
          this.getUserInfo();
        } else {
          this.getAuthCode();
        }
      },
    });
    this.handleGetAvailableCoins();
  },
  onShow() {
    // if(this.data.token) {
    //   this.getUserData()
    // }
  },
  // 登录失效验证
  tokenInvalid(code) {
    // token失效
    if (code == 401 || code == 400 || code == 100010) {
      my.removeStorage({
        key: "token",
      });
      const currentRetryCount = this.data.retryCount;
      // 使用指数退避策略
      const backoffTime = Math.min(1000 * Math.pow(2, currentRetryCount - 1), 10000);
      console.log("backoffTime", backoffTime);
      // 显示加载提示
      my.showLoading({
        content: "Reconnecting...",
      });
      this.data.retryCount = this.data.retryCount + 1;
      this.setData({
        retryCount: this.data.retryCount,
      });
      // 延迟执行重试
      setTimeout(() => {
        my.hideLoading();
        this.getAuthCode();
      }, backoffTime);

      return "token_expired";
    }
    // 系统维护
    if (code == 102121) {
      this.setData({
        showMessageConfrim: true,
        showMessageTitle: "System Maintenance",
        showMessagePagrm: "Since you have not operated on for a long time, please log in again.",
      });
      return "system_maintenance";
    }
    if (code == 102020) {
      return "Network error";
    }
    // IP限制
    if (code == 1000051) {
      this.setData({
        ip_restricted: true,
      });
      return "ip_restricted";
    }
    return "token_valid";
  },
  // 获取authCode并登录
  getAuthCode() {
    // 不再需要在这里检查重试次数，已在tokenInvalid中处理
    my.showLoading();
    let that = this;
    my.getAuthCode({
      scopes: "auth_user",
      success: (res) => {
        res.authCode = res.authCode || debugAuthCode || "";
        if (doGetAuthCode) {
          my.alert({
            content: res.authCode,
          });
        }
        my.hideLoading();
        if (res.authCode) {
          // console.log('res.authCode =======', res.authCode)
          that.setData({
            gcashAuthCode: res.authCode,
          });
          that.playLogin(res.authCode);
        } else if (res.authErrorScopes.auth_user == 4006) {
          console.log(res);
          this.setData({
            forbid_access_no_button: true,
          });
        } else {
          my.alert({
            content: "fail to get authCode, please try to reload, error:" + res.authErrorScopes.auth_user,
          });
        }
      },
      fail: (res) => {
        console.log(res);
        my.showToast({
          type: "fail",
          content: "fail to get getAuthCode, please try to reload, error:" + res.authErrorScopes.auth_user,
          duration: 2000,
        });
        this.tokenInvalid(res.status);
        my.hideLoading();
      },
    });
  },
  async playLogin(authCode) {
    if (this.isRetryMaxFn()) {
      return;
    }
    if (this.data.isLoginloading) {
      return;
    }

    this.setData({
      isLoginloading: true,
    });

    try {
      const result = await apiService.login(authCode, this, this.data.marketChannel);

      this.setData({
        isLoginloading: false,
      });

      if (result.success) {
        const resdata = result.data;
        resdata.user_info.balance = this.formatCurrency(resdata.user_info.balance);
        resdata.user_info.is_vip = Number(resdata.user_info.is_vip);

        // 保存token
        my.setStorage({
          key: "token",
          data: resdata.token,
        });
        // 保存authcode
        my.setStorage({
          key: "authCode",
          data: authCode,
        });
        // 保存userid
        my.setStorage({
          key: "currentId",
          data: resdata.user_info.id,
        });

        this.setData({
          currentId: resdata.user_info.user_id,
          token: resdata.token,
          userInfo: resdata.user_info,
          retryCount: 0,
          activityPopsInfo: {
            token: resdata.token,
            isVip: resdata.user_info.is_vip,
            register_award: resdata.register_award,
            is_register: resdata.user_info.is_register,
          },
          // activityPopsInfo: {
          //   isVip: 0,
          //   is_register: 1,
          //   register_award: {
          //     amount: "20.00",
          //     type: 3
          //   },
          //   token: resdata.token
          // }
        });

        this.data.retryCount = 0;
        this.getUserData();
        // 获取新的banner
        this.getBannerList("update");
      } else {
        // 登录失败，错误已由 apiService 自动处理
        const token_status = this.tokenInvalid(result.data.code);

        // 其他错误 登录失败, token验证成功但是code不是200(登陆失败)
        if (token_status == "token_valid" && result.data.code != 200) {
          my.removeStorage({
            key: "token",
          });
          // 避免多次获取banner
          if (this.data.retryCount == 0) {
            this.getBannerList("update");
          }
          this.data.retryCount = this.data.retryCount + 1;
          this.setData({
            retryCount: this.data.retryCount,
          });
          this.getAuthCode();
          // IP限制
          if (result.data.code == 1000051) {
            this.setData({
              ip_restricted: true,
            });
          }
          // 未满21岁
          if (result.data.code == 102043) {
            this.setData({
              forbid_access_no_button: true,
            });
          }
          // 账号锁定
          if (result.data.code == 102008) {
            this.setData({
              account_locked: true,
              account_locked_msg: result.data.msg,
              customer_email: result.data.data.customer_email,
            });
          }
          // 账号黑名单
          if (result.data.code == 101013) {
            this.setData({
              account_locked: true,
              account_locked_msg: result.data.msg,
              customer_email: result.data.data.customer_email,
            });
          }
        }
      }
    } catch (error) {
      console.error("登录失败:", error);
      this.setData({
        isLoginloading: false,
      });
      my.showToast({
        type: "fail",
        content: "fail to login, please try to reload",
        duration: 2000,
      });
    }
  },
  clickTopUpBatches(e) {
    this.setData({
      topUpBatched: e.detail.value.length > 0,
      topUpBatchColor: e.detail.value.length > 0 ? "#AC1140" : "#ffffff",
    });
  },
  // 重试最大次数
  isRetryMaxFn() {
    // 增加重试计数
    const currentRetryCount = this.data.retryCount;
    // 如果超过最大重试次数，显示消息
    if (currentRetryCount > 2) {
      this.setData({
        showMessageConfrim: true,
        showMessageTitle: "Login Expired",
        showMessagePagrm: "Since you have not operated on for a long time, please log in again.",
      });
      return true;
    }
    return false;
  },
  // 获取用户信息
  async getUserInfo() {
    try {
      const response = await apiService.getUserInfo(this.data.token, this);

      if (doGetAuthCode) {
        this.getAuthCode();
      }

      const token_status = this.tokenInvalid(response.data.code);

      // 登录失败
      if (token_status == "token_valid" && response.data.code != 200) {
        my.removeStorage({
          key: "token",
        });
        this.getAuthCode();
        return;
      }

      if (response.data.code == 200) {
        if (response.data.data == false) {
          my.removeStorage({
            key: "token",
          });
          this.getAuthCode();
          return;
        }

        const user_info = response.data.data;
        // 在首次登陆时,userid是否可能为空?
        // 验证是否更换账号, 更换重新登录
        // if (user_info.id != this.data.currentId) {
        //   console.log('更换了账号')
        //   this.getAuthCode()
        //   return
        // }
        user_info.balance = this.formatCurrency(user_info.balance);
        user_info.is_vip = Number(user_info.is_vip);

        this.setData({
          userInfo: user_info,
          currentId: user_info.user_id,
          activityPopsInfo: {
            token: this.data.token,
            isVip: user_info.is_vip,
            register_award: user_info.register_award,
            is_register: user_info.is_register,
          },
          // activityPopsInfo: {
          //   isVip: 0,
          //   is_register: 1,
          //   register_award: {
          //     amount: "20.00",
          //     type: 3
          // },
          // token: this.data.token
          // }
        });

        this.getUserData();
        // 获取缓存的banner
        this.getBannerList("local");
      }
    } catch (error) {
      console.error("获取用户信息失败:", error);
      // 注意：apiService 已经处理了大部分错误，这里主要处理网络异常
      if (error.status) {
        this.tokenInvalid(error.status);
      }
    }
  },
  getURLParams() {
    my.request({
      url: this.data.baseUrl + "api/gcash/get/jumpType",
      method: "POST",
      headers: {
        terminal: APP_DEVICE.terminal,
      },
      data: {
        token: this.data.token,
      },
      dataType: "json",
      success: (res) => {
        if (res.data.code == 200) {
          // 10规定特殊弹框不处理的数字
          if (res.data.date.type === 10) {
            this.showDepositMask({});
            return;
          }
          if (res.data.date.type) {
            this.showDepositMask(res.data.date.type);
          } else {
            // this.open21YearsOldDialog()
          }
        }
      },
    });
  },
  getEnum() {
    // 获取后台活动配置内容
    my.request({
      url: this.data.baseUrl_avt + "api/activity/adjustment/enum",
      method: "GET",
      headers: {
        terminal: APP_DEVICE.terminal,
      },
      data: {
        token: this.data.token,
      },
      dataType: "json",
      success: (res) => {
        if (res.data.code == 200) {
          this.setData({
            awardTypes: res.data.data,
            activityPopsInfo: {
              ...this.data.activityPopsInfo,
              awardTypes: res.data.data,
            },
          });
        }
      },
    });
  },
  getUserData() {
    this.getEnum();
    this.setAvailableCoins();
    this.getUserKyc();
    this.getRechargeWithdrawalList(1);
    this.getRechargeWithdrawalList(2);
    this.getDownloadGuideConfig(); // 获取下载引导配置
  },

  // 获取下载引导浮标配置
  async getDownloadGuideConfig() {
    // 如果没有 token，跳过获取配置
    if (!this.data.token) {
      console.log("未登录，跳过获取下载引导配置");
      return;
    }

    try {
      const response = await apiService.getDownloadGuideConfig(this.data.token, this);

      if (response.data && response.data.code == 200 && response.data.data) {
        // 合并后台配置和默认配置
        this.setData({
          downloadGuideConfig: {
            show: !!response.data.data.download_url,
            content: response.data.data.slogan,
            link: response.data.data.download_url,
          },
        });
      }
    } catch (error) {
      console.log("获取下载引导配置失败，使用默认配置:", error);
      // 失败时保持默认配置
    }
  },

  // 点击下载引导浮标
  onDownloadGuideClick() {
    const { link } = this.data.downloadGuideConfig;
    if (link) {
      // 如果有链接，则跳转
      this.playGameWithType({
        currentTarget: {
          dataset: {
            type: "external",
            url: link,
          },
        },
      });
    }
  },

  // 打开21岁弹窗
  open21YearsOldDialog() {
    this.setData({
      is_21_years_old: true,
    });
  },
  // 排序
  sortByDate(a, b) {
    return Number(a.amount) - Number(b.amount);
  },
  // 获取首充配置 对照表
  getRechargeRule(notis, first_restriction) {
    my.request({
      url: this.data.baseUrl + "api/global-config/first/recharge/rule",
      method: "POST",
      headers: {
        terminal: APP_DEVICE.terminal,
      },
      data: {
        token: this.data.token,
        // token: null
      },
      dataType: "json",
      success: (res) => {
        this.tokenInvalid(res.data.code);
        if (res.data.code == 200) {
          const recharge = res.data.data.recharge.sort(this.sortByDate);
          this.data.depositList.tags = this.data.depositList.tags.sort(this.sortByDate);
          this.setData({
            is_first_charge: enableDebugMode ? true : res.data.data.is_first_charge, // 调试模式强制开启首充
          });
          // 首充配置
          if (this.data.is_first_charge) {
            // --debug
            this.data.depositList.min = first_restriction;
            this.data.depositList.tags.forEach((item) => {
              const matchedRecharge = recharge.find((current, index) => {
                const next = recharge[index + 1];
                if (index === recharge.length - 1) {
                  return item.amount >= current.amount;
                }
                // 处理中间区间: 当前金额 >= 区间下限 且 < 区间上限
                return item.amount >= current.amount && item.amount < next.amount;
              });
              // 过滤符合最小值区间的充值
              const isMin = item.amount >= first_restriction;

              // 如果符合最小值区间 且 符合充值区间 则设置奖励
              if (matchedRecharge && isMin) {
                item.award = matchedRecharge.award;
                item.id = matchedRecharge.id;
              }
            });
          }

          // 格式化千分符号充值列表
          const formatDepositTagsList = this.data.depositList.tags.map((item) => {
            return {
              ...item,
              amount: item.amount,
              showAmount: this.formatCurrency(+item.amount, false),
            };
          });
          const formatDepositList = {
            ...this.data.depositList,
            tags: formatDepositTagsList,
          };
          this.setData({
            depositList: formatDepositList,
            rechargeRule: recharge,
          });
          // 跳转来的有参数 则打开弹框
          if (notis != "notis") {
            this.getURLParams();
          }
        }
      },
      fail: (res) => {
        this.tokenInvalid(res.status);
      },
    });
  },
  handleBannerLoad(e) {
    // console.log(e)
  },
  // 获取banner
  getBannerList(type) {
    let localBannerList = [];
    if (type == "local") {
      my.getStorage({
        key: "bannerList",
        success: (res) => {
          if (res.data && res.data.length > 0 && res.data[0].channel.includes("GcashMini")) {
            // this.setData({
            //   bannerList: res.data
            // })
            localBannerList = res.data;
          } else {
            this.getBannerList("update");
          }
        },
      });
    }

    my.request({
      url: this.data.baseUrl_avt + "api/banner/activity/list",
      method: "POST",
      headers: {
        terminal: APP_DEVICE.terminal,
      },
      data: {
        token: this.data.token,
      },
      dataType: "json",
      success: (res) => {
        if (res.data.code == 200) {
          let bannerList = [
            // {
            //   "id": "81794634161811461",
            //   "channel": "GcashMini",
            //   "page": "Home,Promo",
            //   "sort": 0,
            //   "title": "test",
            //   "home_page_banner": "images/hpb_248b0e958a274dbe8d36db74d6c2161d.jpeg",
            //   "promo_page_banner": "images/ppb_5b8a89add29749dda146595be22b6812.jpeg",
            //   "gcash_mini_banner": "images/ppb_480e923b5acd4d0688f82cdc68381c80.jpg",
            //   "jump_type": 4,
            //   "duration": 0,
            //   "url": "",
            //   "activity_list": "2",
            //   "status": 1,
            //   "start_at": "2025-04-24 00:00:00",
            //   "end_at": "2025-04-25 00:00:00",
            //   "created_at": "2025-04-24 10:37:03",
            //   "updated_at": "2025-04-24 10:37:03",
            //   "operator": "admin",
            //   "choices": "[\"2\"]",
            //   "picture_id": 0,
            //   "gid": "",
            //   "game_type": "",
            //   "provider_list": null,
            //   "picture_details_title": "",
            //   "picture_details": "",
            //   "maintained": 0,
            //   "hidden": 0,
            //   url: `${this.data.assetsUrl}images/ppb_5b8a89add29749dda146595be22b6812.jpeg`,
            // },
            // {
            //   "id": "80747844226478086",
            //   "channel": "Web,Maya,Gcash",
            //   "page": "Home,Promo",
            //   "sort": 11,
            //   "title": "YB Extra 0.35% Cashback",
            //   "home_page_banner": "images/hpb_9558acd046e64d8e955be15353fc5d9a.jpg",
            //   "promo_page_banner": "images/ppb_480e923b5acd4d0688f82cdc68381c80.jpg",
            //   "gcash_mini_banner": "images/ppb_480e923b5acd4d0688f82cdc68381c80.jpg",
            //   "jump_type": 4,
            //   "duration": 5,
            //   "url": "",
            //   "activity_list": "14",
            //   "status": 1,
            //   "start_at": "2025-04-21 00:00:00",
            //   "end_at": "2026-04-21 00:00:00",
            //   "created_at": "2025-04-21 13:17:29",
            //   "updated_at": "2025-04-24 13:17:29",
            //   "operator": "nstest",
            //   "choices": "[\"1\", \"2\"]",
            //   "picture_id": 0,
            //   "gid": "",
            //   "game_type": "",
            //   "provider_list": null,
            //   "picture_details_title": "",
            //   "picture_details": "",
            //   "maintained": 0,
            //   "hidden": 0,
            //   url: `${this.data.assetsUrl}images/hpb_9558acd046e64d8e955be15353fc5d9a.jpg`,
            // },
          ];
          if (res.data.data && res.data.data.banner && res.data.data.banner.length > 0) {
            const localBannerUpdates = localBannerList.map((f) => f.updated_at);

            bannerList = res.data.data.banner
              .map((item) => {
                if (item.channel.includes("GcashMini")) {
                  // 判断缓存是否包含最新banner
                  return {
                    ...item,
                    // 自定义跳转配置时，none \ internal URl \Outside URL 都统一跳转promos页面
                    jump_type: item.home_page_jump == 1 || (item.home_page_jump == 2 && ["0", "1", "2"].includes(`${item.jump_type}`)) ? "promos" : "banner",
                    url: `${this.data.assetsUrl}${item.gcash_mini_banner || item.home_page_banner}`,
                  };
                }
              })
              .filter((item) => item);

            const newBannerUpdates = bannerList.map((f) => f.updated_at);
            // 查找新banner有而本地banner没有的值
            const difference1 = newBannerUpdates.filter((x) => !localBannerUpdates.includes(x));
            const difference2 = localBannerUpdates.filter((x) => !newBannerUpdates.includes(x));
            if (localBannerList.length !== bannerList.length || difference1.length > 0 || difference2.length > 0) {
              const sortBannerList = bannerList.sort((a, b) => b.sort - a.sort);
              console.log("sortBannerList", sortBannerList);
              this.setData({
                bannerList: sortBannerList,
              });
              my.setStorage({
                key: "bannerList",
                data: sortBannerList,
                success: () => {},
              });
            } else {
              this.setData({
                bannerList: localBannerList,
              });
            }
          }
        } else {
          my.showToast({
            type: "fail",
            content: res.data.message,
            duration: 2000,
          });
        }
      },
      fail: (res) => {
        this.tokenInvalid(res.status);
      },
    });
  },
  // 获取消息
  async getNotification() {
    try {
      const response = await apiService.getMarqueeList(this);

      if (response.data && response.data.data) {
        // 将 HTML 转换为纯文本
        const type = 3;
        const notificationText = response.data.data
          .filter((item) => this.getBitValue(item.channel, type) == 1)
          .map((item) => this.stripHtml(item.content))
          .join(";  ");
        // 设置文本后，计算动画时长
        this.setData(
          {
            notificationText,
          },
          () => {
            this.calculateMarqueeSpeed();
          }
        );
      }
    } catch (error) {
      console.error("获取通知消息失败:", error);
    }
  },
  // 处理位运算
  getBitValue(num, position) {
    // 确保 num 是32位整数，position在0到31之间
    if (Number.isInteger(num) && num >= -2147483648 && num <= 2147483647 && Number.isInteger(position) && position >= 0 && position <= 31) {
      // 右移位数，将目标位移动到最低位，然后与1进行按位与操作
      const shiftedValue = (num >> (position - 1)) & 1;
      return shiftedValue;
    } else {
      console.error("参数不合法");
      return -1; // 返回一个错误值或者其他适当的值
    }
  },
  // 添加去除 HTML 标签和常见HTML实体的方法
  stripHtml(html) {
    if (!html) return "";

    return (
      html
        // 移除HTML标签
        .replace(/<[^>]*>/g, "")
        // 替换常见HTML实体
        .replace(/&nbsp;/g, " ")
        .replace(/&amp;/g, "&")
        .replace(/&lt;/g, "<")
        .replace(/&gt;/g, ">")
        .replace(/&quot;/g, '"')
        .replace(/&#39;/g, "'")
        .replace(/&ldquo;/g, '"')
        .replace(/&rdquo;/g, '"')
        .replace(/&middot;/g, "·")
        // 处理数字形式的HTML实体
        .replace(/&#(\d+);/g, function (match, dec) {
          return String.fromCharCode(dec);
        })
    );
  },
  offDetail() {
    this.setData({
      showDetailMask: false,
      showDetailInfo: {},
    });
  },
  offDetailsList(e) {
    this.setData({
      openMergeOrderId: null,
      withdrawalMergeOrderList: [],
      withdrawalMergeOrderItem: MERGE_ITEM_CONSTANT,
    });
  },
  // 根据组合订单id 获取同个id下的所有订单
  getWithdrawalMergeOrders(item = {}) {
    const that = this;
    // 当前页码等于总页码时不需要查询下一页（在当前页非0的情况下
    if (that.data.withdrawalMergeOrderItem.current_page >= that.data.withdrawalMergeOrderItem.total_page && that.data.withdrawalMergeOrderItem.current_page > 0) {
      return;
    }
    my.showLoading();
    const curPage = that.data.withdrawalMergeOrderItem.current_page + 1;
    my.request({
      url: this.data.baseUrl_avt + "api/exchange/" + that.data.openMergeOrderId || item.id,
      method: "GET",
      headers: {
        terminal: APP_DEVICE.terminal,
      },
      data: {
        token: this.data.token,
        page: curPage,
        page_number: 15,
      },
      dataType: "json",
      fail: (err) => {
        console.log("api/exchange/", err);
        my.hideLoading();
        my.showToast({
          type: "fail",
          content: err.errorMessage.msg || "fail to load redeem record",
          duration: 2000,
        });
        // if (item.id) {
        //   that.setData({
        //     openMergeOrderId: item.id, //存储组成订单的id
        //   })
        // }
        // const mockList = generateMockData(15, {
        //   "id": "1043228531296460851",
        //   "payment_method": "Maya",
        //   "order_no": "*****************",
        //   "account_no": "",
        //   "total_amount": 10,
        //   "remark": "1",
        //   "paid_at": "2025-06-25 14:36:09",
        //   "ship_status": 2,
        //   "audit_status": 2,
        //   "created_at": "2025-06-25 14:36:09",
        //   "status_desc": "Successful",
        //   "sys_remark": "Adjustment",
        //   "ship_fail_code": 0,
        //   "ship_fail_message": "",
        //   "err_msg": ""
        // }, 'id');

        // // mock data
        // const data = {
        //   "code": 200,
        //   "msg": "success",
        //   "data": {
        //     "suc_amount": 300000,
        //     "total_amount": 500000,
        //     "combine_order_no": "*****************",
        //     "list": mockList,
        //     "domain": "http:\/\/uat-nustar-static.nustaronline.vip\/",
        //     "count": 44,
        //     "current_page": curPage,
        //     "total_page": 3
        //   }
        // }
        // const newList = that.hanldeWithdrawalDatas(data.data.list || []);
        // const resultList = that.data.withdrawalMergeOrderList.concat(newList);
        // console.log("withdrawalMergeOrderList", resultList, newList)
        // const {
        //   suc_amount,
        //   total_amount
        // } = data.data;
        // this.setData({
        //   withdrawalMergeOrderList: resultList,
        //   withdrawalMergeOrderItem: {
        //     ...data.data,
        //     suc_amount: that.formatCurrency(suc_amount / 100, false),
        //     total_amount: that.formatCurrency(total_amount / 100, false),
        //   }
        // })
      },
      success: (res) => {
        if (res.data.code == 200) {
          if (!res.data.data || res.data.data.list.length <= 0) return;
          const newList = that.hanldeWithdrawalDatas(res.data.data.list);
          const resultList = that.data.withdrawalMergeOrderList.concat(newList);
          const { suc_amount, total_amount } = data.data;
          if (item.id) {
            that.setData({
              openMergeOrderId: item.id, //存储组成订单的id
            });
          }
          that.setData({
            withdrawalMergeOrderList: resultList,
            withdrawalMergeOrderItem: {
              ...res.data.data,
              suc_amount: that.formatCurrency(suc_amount / 100, false),
              total_amount: that.formatCurrency(total_amount / 100, false),
            },
          });
        }
        my.hideLoading();
      },
    });
  },
  // 打开详情
  openDetail(e) {
    const detail = e.target.dataset.item;
    console.log(detail);
    if (this.data.openMergeOrderId) {
      detail.created_at = this.formatDateTime(detail.created_at);
      detail.account_no = this.maskPhoneNumber(detail.account_no);
      detail.total_amount = this.formatCurrency(detail.total_amount, true, false);
    } else {
      if (detail.quantity > 1) {
        // 展示分批提现合并订单列表详情
        this.getWithdrawalMergeOrders(detail);
        return;
      }
      if (this.data.activePicker == 0) {
        detail.created_at = this.formatDateTime(detail.created_at);
        detail.amount = this.formatCurrency(detail.amount, true, true);
      } else if (this.data.activePicker == 1) {
        detail.created_at = this.formatDateTime(detail.created_at);
        detail.account_no = this.maskPhoneNumber(detail.account_no);
        detail.total_amount = this.formatCurrency(detail.total_amount, true, false);
      } else if (this.data.activePicker == 2) {
        detail.updated_at = this.formatDateTime(detail.updated_at);
        detail.amount = this.formatCurrency(detail.amount, true, true);
      }
    }

    this.setData({
      showDetailMask: true,
      showDetailInfo: detail,
    });
  },
  formatDateTime(dateTimeStr) {
    const date = new Date(dateTimeStr.replace(" ", "T"));
    const formattedDate = date.toLocaleDateString("en-US", {
      month: "short", // 月份缩写（如 "Jun"）
      day: "2-digit", // 两位数日期（如 "16"）
      year: "numeric", // 年份（如 "2023"）
    });
    const formattedTime = date.toLocaleTimeString("en-US", {
      hour: "2-digit", // 两位数小时
      minute: "2-digit", // 两位数分钟
      second: "2-digit", // 添加秒
      hour12: false, // 使用 24 小时制
    });
    return `${formattedDate} ${formattedTime}`;
  },
  handleCopy(e) {
    const text = e.target.dataset.text || "";
    my.setClipboard({
      text: text,
      success: (res) => {
        my.showToast({
          type: "success",
          content: "copy success",
          duration: 2000,
        });
      },
      fail: (res) => {
        my.showToast({
          type: "fail",
          content: "copy fail",
          duration: 2000,
        });
      },
    });
  },
  copyAccountLockedMsg(e) {
    my.setClipboard({
      text: this.data.customer_email,
      success: (res) => {
        my.showToast({
          type: "success",
          content: "copy success",
          duration: 2000,
        });
      },
      fail: (res) => {
        my.showToast({
          type: "fail",
          content: "copy fail",
          duration: 2000,
        });
      },
    });
  },
  maskPhoneNumberKYC(phoneNumber) {
    // 从倒数第二位开始替换 4 位为 *
    const maskedNumber = phoneNumber.slice(0, -7) + "****" + phoneNumber.slice(-3);
    return maskedNumber;
  },
  maskPhoneNumber(phoneNumber) {
    // 从倒数第二位开始替换 4 位为 *
    const maskedNumber = phoneNumber.slice(0, -6) + "****" + phoneNumber.slice(-2);
    return maskedNumber;
  },
  // KYCxinxi
  getUserKyc() {
    my.request({
      url: this.data.baseUrl + "api/get/user/kyc",
      method: "POST",
      data: {
        token: this.data.token,
      },
      headers: {
        terminal: APP_DEVICE.terminal,
      },
      dataType: "json",
      success: (res) => {
        if (res.data.code == 200) {
          // 处理手机号
          res.data.data.phone = this.maskPhoneNumberKYC(res.data.data.phone);
          res.data.data.month_en = this.getMonthName(res.data.data.month);
          this.setData({
            KYCuserInfo: res.data.data,
          });
        }
      },
    });
  },
  // tool 工具转换
  getMonthName(month) {
    if (!month) {
      return "";
    }
    const date = new Date(2023, month - 1, 1);
    return date.toLocaleString("en-US", {
      month: "short",
    });
  },
  // 获取充值列表
  showDepositMask(depositValue = "") {
    // 获取充值填充最大值，用于判断连续充值最大值
    my.getStorage({
      key: "maximum_single_recharge",
      success: (res) => {
        if (res.data)
          this.setData({
            maximum_single_recharge: res.data,
          });
        my.removeStorage({
          key: "maximum_single_recharge",
        });
      },
    });
    const isObject = typeof depositValue == "object";
    console.log(isObject);
    this.setData({
      showDepositMask: true,
      depositValue: isObject ? "" : depositValue,
    });
    console.log(depositValue);
    if (!isObject) {
      this.bindDepositValue({
        detail: {
          value: depositValue,
        },
      });
    }
    // my.showLoading();
  },
  offDepositMask() {
    this.setData({
      showDepositMask: false,
      selectDepositIndex: null,
      depositValue: 0,
      depositAward: 0,
      selectedConfigId: 0,
      validInputValue: true,
      validInputValueText: "",
    });
  },
  selectDeposit(e) {
    const index = e.target.dataset.index;
    this.setData({
      selectDepositIndex: index,
      validInputValue: true,
      depositValue: Number(this.data.depositList.tags[index].amount),
      depositAward: Number(this.data.depositList.tags[index].award),
      selectedConfigId: this.data.depositList.tags[index].id,
      validInputValueText: "",
    });
    this.calculateTotalDepositValue(this.data.depositValue, this.data.depositAward);
  },
  calculateTotalDepositValue(depositValue, depositAward) {
    // 获取当前选择的充值数字金额
    const depositNumberValue = Number(depositValue);
    // 获取当前选择的充值千分格式金额
    const depositCurrencyValue = this.formatCurrency(depositNumberValue, false);
    // 获取当前选择的充值赠送金额
    const depositAwardValue = Number(depositAward);
    // 计算总充值金额
    const totalDepositValue = this.formatCurrency(depositNumberValue + depositAwardValue, false);
    this.setData({
      depositCurrencyValue,
      totalDepositValue,
    });
  },
  // handleFocus
  handleFocus() {
    console.log("focus");
    this.setData({
      isFocus: true,
    });
  },
  // handleBlur
  handleBlur() {
    console.log("blur");
    this.setData({
      isFocus: false,
    });
  },
  // 绑定充值值
  bindDepositValue(e) {
    // if(e.detail.value == '') {
    //   this.setData({
    //     depositValue: '',
    //     validInputValue: true,
    //     validInputValueText: '',
    //     selectDepositIndex: -1
    //   })
    //   return
    // }
    let value = Number(e.detail.value);
    // 显示限制最大安全值
    if (value > Number.MAX_SAFE_INTEGER) {
      value = Number.MAX_SAFE_INTEGER;
    }
    // 检查value不为数字 则返回
    // if (isNaN(value)) {
    //   console.log(this.data.depositValue)
    //   this.setData({
    //     depositValue: this.data.depositValue
    //   })
    //   return
    // }
    this.setData({
      depositValue: value,
      validInputValue: true,
      validInputValueText: "",
      selectDepositIndex: -1,
    });
    // 验证值的赠送范围 首充
    let depositAward = 0;
    if (this.data.is_first_charge && this.data.rechargeRule.length > 0) {
      const tags = this.data.rechargeRule;
      let selectedConfigId = 0;

      // 小于第一个区间
      if (value < tags[0].amount) {
        depositAward = 0;
      }
      // 大于等于最后一个区间
      else if (value >= tags[tags.length - 1].amount) {
        depositAward = tags[tags.length - 1].award;
        selectedConfigId = tags[tags.length - 1].id;
      }
      // 中间区间
      else {
        for (let i = 0; i < tags.length - 1; i++) {
          const currentItem = tags[i];
          const nextItem = tags[i + 1];
          if (value >= currentItem.amount && value < nextItem.amount) {
            depositAward = currentItem.award;
            selectedConfigId = currentItem.id;
            break;
          }
        }
      }
      this.setData({
        depositAward,
        selectedConfigId,
      });
    }
    this.calculateTotalDepositValue(value, depositAward);
    // 验证整数
    if (!Number.isInteger(value)) {
      console.log("not integer");
      this.setData({
        validInputValue: false,
        validInputValueText: "Please enter a integer value",
      });
      return;
    }
    // 验证值范围
    if (value < this.data.depositList.min || value > this.data.depositList.max) {
      console.log("not in range");
      this.setData({
        validInputValue: false,
        validInputValueText: `value range: ${this.data.depositList.min}-${this.data.depositList.max}`,
      });
      return;
    }
    // 找当前值对应的索引
    for (let i = 0; i < this.data.depositList.tags.length; i++) {
      const item = this.data.depositList.tags[i];
      if (item.amount == value) {
        this.setData({
          selectDepositIndex: i,
        });
        return;
      }
    }
  },

  // 显示充值提现小弹窗
  confrimDepositOrWithdraw() {
    const index = this.data.selectDepositIndex;
    //判断是否大于等于连续充值最大值，是则自动勾选连续充值选框
    // const depositValue = this.data.depositValue;
    // const shouldBatch = this.data.maximum_single_recharge > 0 && depositValue >= this.data.maximum_single_recharge;
    this.setData({
      // topUpBatched: this.data.topUpBatched || shouldBatch,
      // topUpBatchColor: this.data.topUpsBatched || shouldBatch ? "#AC1140" : "#ffffff", //同时更新checkbox的背景色
      showDepositOneMask: true,
    });
  },

  offDepositOneMask() {
    this.setData({
      showDepositOneMask: false,
    });
  },
  onCloseDepositPopup() {
    console.log("onCloseDepositPopup");
    this.setData({
      deposit_award: "",
    });
  },
  doDeposit() {
    let that = this;
    my.showLoading();
    // 请求提现接口
    if (that.data.showRedeemMask) {
      my.request({
        url: that.data.baseUrl + "api/exchange",
        method: "POST",
        headers: {
          terminal: APP_DEVICE.terminal,
        },
        data: {
          token: that.data.token,
          app_package_name: APP_GCASH.packageName,
          app_version: APP_VERSION,
          amount: that.data.depositValue,
          product_type: 3,
          quantity: 1,
          price: that.data.depositValue,
        },
        dataType: "json",
        success: (res) => {
          my.hideLoading();
          this.tokenInvalid(res.data.code);
          if (res.data.code == 200) {
            this.showConfrimMask(res.data.msg, "Congratulations", "Play");
            setTimeout(() => {
              this.setAvailableCoins();
            }, 500);
          } else {
            my.alert({
              content: res.data.msg,
            });
          }
        },
        fail: (res) => {
          my.showToast({
            type: "fail",
            content: res.data.errorMessage || "Server connection error",
            duration: 3000,
          });
          my.hideLoading();
          console.log(res);
          this.tokenInvalid(res.status);
        },
      });
    }

    // 请求充值接口
    if (that.data.showDepositMask) {
      my.request({
        url: that.data.baseUrl + "api/payment/balance-add",
        method: "POST",
        headers: {
          terminal: APP_DEVICE.terminal,
        },
        data: {
          token: that.data.token,
          app_package_name: APP_GCASH.packageName,
          app_version: APP_VERSION,
          amount: that.data.depositValue,
          award: that.data.is_first_charge ? that.data.depositAward : 0,
          config_id: that.data.is_first_charge ? that.data.selectedConfigId : 0,
          identifier: "gcashpay",
        },
        dataType: "json",
        success: (res) => {
          my.hideLoading();
          this.tokenInvalid(res.data.code);
          if (res.data.code == 200) {
            const orderId = res.data.data.paySerialNo;
            try {
              // 调用支付SDK
              my.tradePay({
                paymentUrl: res.data.data.paymentUrl,
                success: (res1) => {
                  // 调用平台Gcash支付回调通知接口
                  if (res1.resultCode == 9000) {
                    // 成功dialog
                    let msg = "";
                    if (this.data.is_first_charge) {
                      msg = "You have successfully claimed your bonus.Best of luck on your journey ahead!";
                      // 设置首充奖励弹窗
                      if (that.data.depositAward) {
                        this.setData({
                          deposit_award: that.data.depositAward,
                        });
                      }
                    } else {
                      msg = "Top-Up Successful! Enjoy Your Nustar Online Gaming Experience!";
                    }
                    // 充值后强制重置首充字段为false
                    this.setData({
                      is_first_charge: false,
                    });
                    // if (this.data.topUpBatched) {
                    //   // 勾选分批次充值要一直显示充值弹窗并填充金额
                    //   this.batchesTopupHandle(msg);
                    //   return;
                    // }
                    this.showConfrimMask(msg, "Congratulations", "Play");
                    // 刷新余额
                    this.setAvailableCoins();
                  } else {
                    this.showConfrimMask("The payment was unsuccessful due to an abnormality. Please try again later.", "Oops");
                    // my.alert({
                    //   content: 'The payment was unsuccessful due to an abnormality. Please try again later.',
                    // });
                  }
                  // 确保隐藏 loading
                  my.hideLoading();
                  // 发送充值通知 无论成功与否
                  this.getRechangeNotis(orderId, res.resultCode);
                },
                fail: (res) => {
                  // 确保隐藏 loading
                  my.hideLoading();
                  this.showConfrimMask("The payment was unsuccessful due to an abnormality. Please try again later.", "Oops");
                  // my.alert({
                  //   content: 'fail to call tradePay',
                  // });
                },
              });
            } catch (error) {
              console.log("调用支付", error);
              // 确保隐藏 loading
              my.hideLoading();
            }
          } else {
            // 确保隐藏 loading
            my.hideLoading();
            my.alert({
              content: res.data.msg,
            });
          }
        },
        fail: (res) => {
          my.hideLoading();
          my.showToast({
            type: "fail",
            content: res.data.msg || "fail to start payment",
            duration: 2000,
          });
          this.tokenInvalid(res.status);
        },
      });
    }
  },

  // 发送充值通知
  getRechangeNotis(id, status) {
    my.request({
      url: this.data.baseUrl + "api/pay-service/recharge-notify/" + id,
      method: "POST",
      headers: {
        terminal: APP_DEVICE.terminal,
      },
      data: {
        acquirementStatus: status,
      },
      success: (res) => {
        my.hideLoading();
        if (res.data.code == 200) {
          // close them dialog
          this.offDepositOneMask();
          this.offDepositMask();
          this.getRechargeWithdrawalList(1, "notis");
          // update to balance
          this.setAvailableCoins();
        } else {
        }
      },
      fail: (res) => {
        my.showToast({
          type: "fail",
          content: "Server Error",
          duration: 2000,
        });
        this.tokenInvalid(res.status);
      },
    });
  },

  batchesTopupHandle(pagrm) {
    // 勾选分批次充值要一直显示充值弹窗并填充金额
    // if (pagrm) my.showToast({
    //   type: "success",
    //   content: pagrm,
    //   duration: 2000,
    // });
    // 关闭充值小窗，但是不清空关闭充值列表弹窗
    this.setData({
      showDepositOneMask: false,
      // topUpBatchColor: this.data.topUpBatched ? "#AC1140" : "#ffffff",
    });
    setTimeout(() => {
      this.setAvailableCoins();
    }, 500);
    my.hideLoading();
  },

  // 消息确认弹框
  showConfrimMask(pagrm, title = "Congratulations", btnText = "") {
    // 关闭弹框 并亲空零四变量
    this.setData({
      showMessageConfrim: true,
      showMessageTitle: title,
      showMessageBtnText: btnText,
      showMessagePagrm: pagrm,

      showDepositOneMask: false,

      showRedeemMask: false,
      showDepositMask: false,
      selectDepositIndex: null,
      depositValue: "",
      depositAward: 0,
      selectedConfigId: 0,
    });
    my.hideLoading();
  },
  // 退出小程序
  exitMiniProgram() {
    this.setData({
      showMessageConfrim: false,
      showMessageTitle: "",
      showMessageBtnText: "",
      showMessagePagrm: "",
      retryCount: 0,
      is_21_years_old: false,
    });
    console.log("current SDK version:", my.SDKVersion);
    if (my.exit) {
      console.log("my.exit");
      my.exit();
    } else if (my.exitApplication) {
      console.log("exitApplication");
      my.exitApplication();
    } else if (my.exitMiniProgram) {
      console.log("exitMiniProgram");
      my.exitMiniProgram();
    } else if (my.onAppHide) {
      console.log("onAppHide");
      my.onAppHide();
    } else {
      console.log("降级方案");
      my.navigateBack({
        delta: 99,
      });
    }
  },
  // 打开未满21岁禁止访问弹框
  openForbidAccess() {
    this.setData({
      forbid_access: true,
    });
  },
  // 未满21岁禁止访问弹框关闭
  forbidAccessDone() {
    this.setData({
      forbid_access: false,
    });
  },
  // 同意所有条款
  agreeAll() {
    this.setData({
      is_21_years_old: false,
    });
  },
  // 打开链接
  linkto(e) {
    const url = e.currentTarget.dataset.url;
    my.call("navigateToLink", {
      targetLink: url,
      appId: APP_GCASH.appId,
      success: (res) => {
        // my.alert({ content: JSON.stringify(res) });
      },
      fail: (res) => {
        my.alert({
          content: JSON.stringify(res),
        });
      },
    });
  },
  // 关闭消息确认弹框
  offConfrimMask() {
    this.setData({
      showMessageConfrim: false,
      showMessageTitle: "",
      showMessageBtnText: "",
      showMessagePagrm: "",
      retryCount: 0,
    });
  },
  DoneConfrimMask() {
    // 系统维护
    if (this.data.showMessageTitle == "System Maintenance") {
      // 退出小程序
      // my.exitApp();
      this.exitMiniProgram();
      return;
    }
    // token失效
    else if (this.data.showMessageTitle == "Login Expired") {
      this.setData({
        retryCount: 2,
      });
      this.getAuthCode();
    }
    // 充值提现成功
    else if (this.data.showMessageTitle == "Congratulations") {
      if (this.data.showMessageBtnText == "Play") {
        // 充值提现成功后跳转
        this.handleRiskConfirm();
      }
      this.setAvailableCoins();
    }
    // 充值失败 再次弹出充值弹窗并显示之前填入金额   勾选分批次充值也要一直显示充值弹窗并填充金额
    else if (this.data.showMessageTitle == "Oops") {
      this.showDepositMask(this.data.depositCurrencyValue.replace(",", ""));
    }

    this.setData({
      showMessageConfrim: false,
      showMessageTitle: "",
      showMessageBtnText: "",
      showMessagePagrm: "",
    });
  },
  // 点击图标刷新余额
  handleGetAvailableCoins() {
    this.animation = my.createAnimation();
    // 节流函数，限制函数在指定时间内只能执行一次
    this.handleRefreshIconClick = throttleFirst(() => {
      try {
        this.animation.rotate(360).step();
        this.setData({
          animation: this.animation.export(),
        });
        this.setAvailableCoins();
      } catch (error) {
        console.log("clickGetAvailableCoins catch error", error);
      } finally {
        setTimeout(() => {
          this.animation.rotate(0).step({
            duration: 0,
          });
          this.setData({
            animation: this.animation.export(),
          });
        }, 1000);
      }
    }, 2000);
  },

  // 设置可用余额
  setAvailableCoins() {
    let that = this;
    that.setData({
      availableCoinsImageClass: "rotating-element",
    });
    my.request({
      url: that.data.baseUrl + "api/jili/backhall",
      method: "POST",
      headers: {
        terminal: APP_DEVICE.terminal,
      },
      data: {
        token: that.data.token,
      },
      dataType: "json",
      success: (res) => {
        my.hideLoading();
        this.tokenInvalid(res.data.code);
        if (res.data.code == 200) {
          // 更新用户当前资产
          that.data.userInfo.balance = that.formatCurrency(res.data.balance);
          that.setData({
            userInfo: that.data.userInfo,
          });
        } else {
          my.hideLoading();
        }
        that.setData({
          availableCoinsImageClass: "",
        });
      },
      fail: (res) => {
        that.setData({
          availableCoinsImageClass: "",
        });
        my.hideLoading();
        this.tokenInvalid(res.status);
      },
    });
  },
  showUserInfo() {
    this.setData({
      showUserInfoMask: true,
    });
  },
  offUserInfo() {
    this.setData({
      showUserInfoMask: false,
      selectDepositIndex: null,
      depositValue: 0,
      depositAward: 0,
      selectedConfigId: 0,
    });
  },
  // 获取充值提现列表 1-充值 2-提现
  getRechargeWithdrawalList(type, notis) {
    my.request({
      url: this.data.baseUrl + "api/global-config/recharge-withdraw",
      method: "POST",
      data: {
        token: this.data.token,
        accountType: "gcashpay",
        // 配置类型 1-充值 2-提现
        type: type,
      },
      headers: {
        terminal: APP_DEVICE.terminal,
      },
      dataType: "json",
      success: (res) => {
        my.hideLoading();
        // res.data.code = 400
        this.tokenInvalid(res.data.code);
        if (res.data.code === 200) {
          if (type === 1) {
            // 充值列表
            const depositList = res.data.data;
            //
            const isForbid = Array.isArray(depositList) && depositList.length == 0;
            if (isForbid) {
              depositList.max = 0;
              depositList.min = 0;
              depositList.tags = [];
              depositList.isForbid = true;
              this.setData({
                depositList,
              });
              return;
            }
            let first_restriction = res.data.data.first_restriction;
            if (res.data.data.first_restriction < 10) {
              first_restriction = res.data.data.min;
            }
            // --debug

            // 整理数据结构
            depositList.tags = depositList.tags
              .map((item) => {
                return {
                  id: "",
                  amount: item,
                  award: 0,
                };
              })
              .filter((item) => {
                return item.amount >= first_restriction;
              });
            this.data.depositList = depositList;
            this.setData({
              depositList,
            });
            this.getRechargeRule(notis, first_restriction);
          }
          if (type === 2) {
            // 如果是空数组[] 则当前禁用
            // 提现列表
            const redeemList = res.data.data;
            const isForbid = Array.isArray(redeemList) && redeemList.length == 0;
            if (isForbid) {
              redeemList.max = 0;
              redeemList.min = 0;
              redeemList.tags = [];
              redeemList.isForbid = true;
              this.setData({
                redeemList,
              });
              return;
            }
            redeemList.tags = redeemList.tags.map((item) => {
              return {
                id: "",
                amount: item,
                showAmount: this.formatCurrency(item, false),
                award: 0,
              };
            });
            this.setData({
              redeemList,
            });
          }
        } else {
          my.showToast({
            type: "fail",
            content: "fail to load redeem options",
            duration: 2000,
          });
        }
        my.hideLoading();
      },
      fail: (res) => {
        my.showToast({
          type: "fail",
          content: res.errorMessage || "fail to load redeem options",
          duration: 2000,
        });
        my.hideLoading();
        this.tokenInvalid(res.status);
      },
    });
  },
  riskDialogRef(ref) {
    console.log("riskDialogRef", ref);
    this.riskDialogRef = ref;
  },
  showRiskDialog() {
    my.showLoading();
    // 请求风控弹窗数据
    my.request({
      url: this.data.baseUrl + "api/withdraw/risk",
      method: "POST",
      data: {
        token: this.data.token,
      },
      headers: {
        terminal: APP_DEVICE.terminal,
      },
      dataType: "json",
      success: (res) => {
        my.hideLoading();
        if (res.data.code == 200) {
          // status == 0 需要弹窗风控
          if (res.data.data.status == 0) {
            this.setData({
              totalBet: parseFloat(res.data.data.target_amount.toFixed(2)),
              currentBet: parseFloat(res.data.data.user_total_valid_bet.toFixed(2)),
            });
            this.riskDialogRef.show();
          }
          // 打开提现弹框 status == 1 直接提现
          if (res.data.data.status == 1) {
            this.showRedeemMask();
          }
        }
      },
      fail: (res) => {
        my.hideLoading();
      },
    });
  },
  handleRiskCancel() {
    console.log("handleRiskCancel");
  },

  handleRiskConfirm() {
    console.log("handleRiskConfirm");
    // 跳转至下注
    const e = {
      target: {
        dataset: {
          type: "bet",
        },
      },
    };
    this.playGameWithType(e);
  },
  // 打开提现
  showRedeemMask() {
    this.setData({
      showRedeemMask: true,
      depositValue: "",
    });
  },
  // 打开提示元素
  openMessageValue() {
    this.setData({
      isMessageOpen: true,
    });
  },
  // 关闭提示元素
  closeMessage() {
    this.setData({
      isMessageOpen: false,
    });
  },
  // 展示提示
  onTapPage() {
    if (this.data.isMessageOpen) {
      this.closeMessage(); // 如果元素已打开，则关闭
    }
  },
  // 选择提现金额
  selectRedeem(e) {
    const index = e.target.dataset.index;
    this.setData({
      selectDepositIndex: index,
      validInputValue: true,
      validInputValueText: "",
      depositValue: this.data.redeemList.tags[index].amount,
    });
    this.calculateTotalDepositValue(this.data.depositValue, 0);
  },
  // 千分金额格式转数字
  formattedAmountToNumber(formattedAmount) {
    if (typeof formattedAmount !== "string") {
      throw new TypeError("输入必须是一个字符串");
    }

    // 移除千分位逗号和可能的货币符号
    const numberString = formattedAmount.replace(/[^\d.-]/g, "");

    // 转换为数字
    const number = parseFloat(numberString);

    // 检查转换结果是否为有效的数字
    if (isNaN(number)) {
      throw new Error("输入的字符串无法转换为有效数字");
    }

    return number;
  },
  // 绑定提现值
  bindRedeemValue(e) {
    const value = Number(e.detail.value);
    this.setData({
      depositValue: value,
      validInputValue: true,
      validInputValueText: "",
    });
    this.calculateTotalDepositValue(value, 0);
    // 找当前值对应的索引
    for (let i = 0; i < this.data.redeemList.tags.length; i++) {
      const item = this.data.redeemList.tags[i];
      if (item.amount == value) {
        this.setData({
          selectDepositIndex: i,
        });
        return;
      }
    }
    this.setData({
      selectDepositIndex: -1,
    });
    // 整数校验
    if (!Number.isInteger(value)) {
      this.setData({
        validInputValue: false,
        validInputValueText: "Please enter a integer value",
      });
      return;
    }
    // 验证值范围
    if (value < this.data.redeemList.min || value > this.data.redeemList.max) {
      this.setData({
        validInputValue: false,
        validInputValueText: `value range: ${this.data.redeemList.min}-${this.data.redeemList.max}`,
      });
      return;
    }
  },
  // 关闭提现弹框
  offRedeemMask() {
    this.setData({
      showRedeemMask: false,

      selectDepositIndex: null,
      depositValue: 0,
      depositAward: 0,
      selectedConfigId: 0,
      validInputValue: true,
      validInputValueText: "",
    });
  },
  // 关闭提现弹框
  offRedeemOneMask() {
    this.setData({
      showRedeemOneMask: false,
    });
  },
  // 打开Transactions弹框
  getPopupTransactionRecords() {
    // 点击之后初始化相关查询条件
    this.setData({
      curTransactionType: "charge",
      transactionTypeIndex: 0,
      transactionDateRealIndex: 0,
      activePicker: 0,
      activeTimer: 0,
      activeStatus: [0],
    });

    // 重置所有交易数据
    this.resetAllTransactionData();

    this.updateStatusArray();
    this.updateActiveStatusText();

    // 默认查询的是充值记录列表信息
    this.getTransactionRecords(0);
  },
  // 获取充值记录和奖励记录
  getTransactionRecords(type) {
    // 获取当前交易数据
    const currentData = this.getCurrentTransactionData();

    // 无更多数据时不做无效查询
    if (currentData.isNoMoreData) return;

    let that = this;
    that.setData({
      showTransactionMask: true,
    });
    my.showLoading();

    let status = that.data.activeStatus;
    if (that.data.activeStatus[0] == 0 || type != 0) {
      status = "";
    }
    if (status) {
      status = status.join(",");
    }

    my.request({
      url: that.data.baseUrl + (type == 0 ? "api/get/recharge/record" : "api/get/award"),
      method: "GET",
      headers: {
        terminal: APP_DEVICE.terminal,
      },
      data: {
        token: that.data.token,
        page: currentData.pageNum,
        page_number: 15,
        // 固定为7天
        date_type: 3,
        status: status || undefined,
      },
      dataType: "json",
      success: (res) => {
        my.hideLoading();
        this.tokenInvalid(res.data.code);
        if (res.data.code == 200) {
          var transactionHistoryList = [];
          var transactionHistoryCount = 0;
          if (res.data.data.hasOwnProperty("list")) {
            transactionHistoryList = res.data.data.list;
            transactionHistoryList.forEach((item) => {
              // 特殊判断reward不需要显示正负号
              item["coins"] = that.formatCurrency(item["amount"], true, true);
              const recharge_status = item.recharge_status + "";
              // success
              if (["1"].includes(recharge_status)) {
                item["status_name"] = "Successful";
                item.color = "status-success";
                item.status_name = "Successful";
              }
              // pending waiting 状态归档到pending
              if (["2", "5", "6", "7", "8"].includes(recharge_status)) {
                item.color = "status-pending";
                item.status_name = "Pending";
              }
              // failure
              if (["3"].includes(recharge_status)) {
                item.color = "status-failure";
                item.status_name = item.err_msg || "Unsuccessful";
              }
              // waiting payment
              // if (['7'].includes(recharge_status)) {
              //   item.color = 'status-waitingpay'
              //   item.status_name = 'Waiting for payment'
              // }

              // deposit
              if (type == 0) {
                item.labStatus = "Top-Up";
              }
              // withdrawal
              if (type == 1) {
                item.labStatus = "Withdrawal";
              }
              // reward
              if (type == 2) {
                item.color = "status-success";
                item.labStatus = "Reward";
                if (that.data.awardTypes.length > 0 && item.update_type) {
                  // 读取后台返回的配置内容
                  let indx = that.data.awardTypes.findIndex((t) => t.change_type == item.update_type);
                  if (indx >= 0) {
                    item.status_name = that.formatStatusName(that.data.awardTypes[indx].title);
                  } else {
                    // 前端自存配置 暂时的兼容处理
                    item.status_name = AWARD_NAME_OBJECTS[item.update_type] || "";
                  }
                }
              }
              // 调账
              if (item.pay_channel == "Adjustment") {
                item.labStatus = "Transfer";
              }
            });
            transactionHistoryCount = res.data.data.count;
          }
          // 处理第一页或者更多页数据的累加
          const newList = currentData.list.concat(transactionHistoryList);
          const groupedList = that.groupByDate(newList);
          const isNoMoreData = res.data.data.total_page <= res.data.data.current_page || res.data.data.total_page == null;

          // 更新当前交易数据
          that.updateCurrentTransactionData({
            list: newList,
            groupedList: groupedList,
            pageNum: currentData.pageNum + 1,
            isNoMoreData: isNoMoreData,
          });

          that.setData({
            transactionHistoryCount: transactionHistoryCount,
            showTransactionMask: true,
          });
          my.hideLoading();
        } else {
          my.showToast({
            type: "fail",
            content: res.data.msg,
            duration: 2000,
          });
          my.hideLoading();
        }
        this.tokenInvalid(res.data.status);
      },
      fail: (res) => {
        my.showToast({
          type: "fail",
          content: res.errorMessage.msg || "fail to load buying coins record",
          duration: 2000,
        });
        my.hideLoading();
        this.tokenInvalid(res.status);
      },
    });
  },
  formatStatusName(status_name) {
    if (!status_name) return "";
    return status_name
      .replace(/cashback/gi, (match) => (match[0] === "C" ? "Rebate" : "rebate"))
      .replace(/payday/gi, "Bonus Time")
      .replace(/casino/gi, "Live Table");
  },
  groupByDate(list) {
    // 创建一个对象，用于按日期分组
    const grouped = {};

    list.forEach((item) => {
      // 提取日期部分（忽略时间）
      let dateStr;
      if (item.created_at) {
        dateStr = item.created_at.split(" ")[0];
      }

      if (item.updated_at) {
        dateStr = item.updated_at.split(" ")[0];
      }

      // 将日期格式化为 "Jan 09, 2025"
      const date = new Date(dateStr);
      const formattedDate = date.toLocaleDateString("en-US", {
        month: "short",
        day: "2-digit",
        year: "numeric",
      });

      // 如果该日期尚未分组，则初始化
      if (!grouped[formattedDate]) {
        grouped[formattedDate] = {
          time: formattedDate,
          data: [],
        };
      }

      // 将当前项添加到对应日期的 data 中
      grouped[formattedDate].data.push(item);
    });

    // 将分组后的对象转换为数组
    return Object.values(grouped);
  },
  // 购买记录上拉触底事件
  handleTransactionScrollToLower(e) {
    // 如果是充值获取充值记录列表信息
    if (this.data.activePicker == 0) {
      this.getTransactionRecords(0);
    }
    // 如果是提现获取提现记录列表信息
    if (this.data.activePicker == 1) {
      this.getRedeemRecords();
    }
    if (this.data.activePicker == 2) {
      this.getTransactionRecords(2);
    }
  },

  doRedeem() {
    let that = this;
    my.showLoading();
    my.request({
      url: that.data.baseUrl + "api/exchange",
      method: "POST",
      headers: {
        terminal: APP_DEVICE.terminal,
      },
      data: {
        id: that.data.selectedRedeemId,
        token: that.data.token,
        product_type: that.data.selectedRedeemType,
        quantity: 1,
      },
      dataType: "json",
      success: function (res) {
        that.offRedeemOneMask();
        that.offRedeemMask();
        that.setAvailableCoins();
        if (res.data.code == 200) {
          my.alert({
            title: "Redeem",
            content: "Your redemption request has been submitted successfully. You will receive in your account within 10 minutes.",
            buttonText: "Ok",
          });
        } else if (res.data.code == 102034) {
          my.alert({
            title: "Redeem",
            content: "Insufficient balance.",
            buttonText: "Ok",
          });
        } else if (res.data.code == 105104) {
          // 提现限额
          my.alert({
            title: "Redeem",
            content: res.data.msg,
            buttonText: "Ok",
          });
        } else if (res.data.code == 1) {
          my.alert({
            title: "Redeem",
            content: res.data.msg,
            buttonText: "Ok",
          });
        } else if (res.data.code == 102037) {
          my.alert({
            title: "Redeem",
            content: res.data.msg,
            buttonText: "Ok",
          });
        } else if (res.data.code == 102038) {
          my.alert({
            title: "Redeem",
            content: res.data.msg,
            buttonText: "Ok",
          });
        } else {
          my.alert({
            title: "Redeem",
            content: "Your redemption request has not been submitted successfully. Please try again.",
            buttonText: "Ok",
          });
        }
        my.hideLoading();
      },
      fail: (res) => {
        my.showToast({
          type: "fail",
          content: "fail to redeem",
          duration: 2000,
        });
        my.hideLoading();
        this.tokenInvalid(res.status);
      },
    });
  },

  hanldeWithdrawalDatas(list) {
    const newList = [...list];
    for (let index = 0; index < newList.length; index++) {
      const item = newList[index];
      item.coins = "-" + this.formatCurrency(item.total_amount);
      item.status_name = item.status_desc;
      item.recharge_status = item.ship_status;
      item.labStatus = "Withdrawal";
      // failure 已拒绝
      if ([3].includes(item.ship_status)) {
        item.status_name = item.err_msg || "Unsuccessful";
        item.color = "status-failure";
        continue;
      }
      // success 已到账
      if ([2].includes(item.ship_status)) {
        item.status_name = "Successful";
        item.color = "status-success";
        continue;
      }
      // pending 待到账
      if ([1].includes(item.ship_status)) {
        if ([1, 2, 4, 5].includes(item.audit_status)) {
          item.status_name = "Pending";
          item.color = "status-pending";
          continue;
        }
        if ([3].includes(item.audit_status)) {
          item.status_name = item.err_msg || "Unsuccessful";
          item.color = "status-failure";
          continue;
        }
      }

      // 提现方式
      if (item.sys_remark == "Adjustment") {
        item.labStatus = "Transfer";
      }
    }
    return newList;
  },

  // 获取提现记录
  getRedeemRecords() {
    // 获取当前交易数据
    const currentData = this.getCurrentTransactionData();

    // 无更多数据时不做无效查询
    if (currentData.isNoMoreData) return;

    let that = this;
    my.showLoading();
    let status = that.data.activeStatus;
    if (that.data.activeStatus[0] == 0) {
      status = "";
    }
    if (status) {
      status = status.join(",") + ",";
    }

    my.request({
      url: that.data.baseUrl + "api/exchange/list",
      method: "GET",
      headers: {
        terminal: APP_DEVICE.terminal,
      },
      data: {
        token: that.data.token,
        status: status,
        // 固定为7天
        date_type: 3,
        page_number: 15,
        page: currentData.pageNum,
      },
      dataType: "json",
      success: (res) => {
        my.hideLoading();
        this.tokenInvalid(res.data.code);
        if (res.data.code == 200) {
          if (res.data.data.list.length <= 0) {
            my.hideLoading();
            return;
          }
          const newList = that.hanldeWithdrawalDatas(res.data.data.list || []);
          // 处理第一页或者更多页数据的累加
          const combinedList = currentData.list.concat(newList);
          const groupedList = that.groupByDate(combinedList);
          const isNoMoreData = res.data.data.total_page <= res.data.data.current_page || res.data.data.total_page == null;

          // 更新当前交易数据
          that.updateCurrentTransactionData({
            list: combinedList,
            groupedList: groupedList,
            pageNum: currentData.pageNum + 1,
            isNoMoreData: isNoMoreData,
          });

          that.setData({
            transactionHistoryCount: res.data.data.count,
          });
        } else {
          my.showToast({
            type: "fail",
            content: "fail to load redeem record",
            duration: 2000,
          });
        }
        my.hideLoading();
      },
      fail: (res) => {
        my.showToast({
          type: "fail",
          content: res.errorMessage.msg || "fail to load redeem record",
          duration: 2000,
        });
        my.hideLoading();
        this.tokenInvalid(res.status);
      },
    });
  },
  offTransactionMask() {
    this.setData({
      showTransactionMask: false,
    });
  },
  // 当前转换值，是否需要保留小数, 是否需要返回正负号
  formatCurrency(number, needDecimal = true, needSign = false) {
    // 输入验证
    if (typeof number !== "number") {
      if (typeof number === "string") {
        number = Number(number);
        if (isNaN(number)) {
          return "----";
        }
      } else {
        return "----";
      }
    }

    // 处理 NaN 和 Infinity
    if (isNaN(number) || !isFinite(number)) {
      return "---";
    }

    // 使用 toFixed() 保留两位小数，并转换为字符串
    let numStr = "";
    if (needDecimal) {
      numStr = (number / 100).toFixed(2);
    } else {
      numStr = number.toFixed(0);
    }

    // 分割整数部分和小数部分
    const parts = numStr.split(".");
    let integerPart = parts[0];
    const decimalPart = parts.length > 1 ? "." + parts[1] : "";

    // 添加千分位分隔符
    let formattedIntegerPart = "";
    const integerLength = integerPart.length;
    for (let i = 0; i < integerLength; i++) {
      formattedIntegerPart += integerPart[i];
      if ((integerLength - 1 - i) % 3 === 0 && i !== integerLength - 1 && integerPart[i] !== "-") {
        //排除负号
        formattedIntegerPart += ",";
      }
    }

    if (needSign) {
      if (number < 0) {
        // formattedIntegerPart = '-' + formattedIntegerPart;
      } else {
        formattedIntegerPart = "+" + formattedIntegerPart;
      }
    }

    return formattedIntegerPart + decimalPart;
  },
  saveHelpDialogRef(ref) {
    this.helpDialogRef = ref;
  },
  openHelpDialog() {
    this.helpDialogRef.show();
  },
  closeHelpDialog() {
    this.helpDialogRef.hide();
    this.onShow();
  },
  // cancelJumpConfrim_1_done() {
  //   this.setData({
  //     showJumpConfrim_1: false
  //   })
  // },
  offOpenLinkDialog() {
    this.setData({
      showJumpConfrim_1: false,
    });
  },
  playGameWithType(e) {
    // 获取数据集，支持 target 和 currentTarget
    const dataset = e.target ? e.target.dataset : e.currentTarget ? e.currentTarget.dataset : {};
    const type = dataset.type;
    this.setData({
      showJumpConfrim_1: true,
    });

    this.data.jumpTypeInfo = {
      type: type,
      id: dataset.id,
      url: dataset.url,
    };
  },
  thousandFormat(x) {
    return x.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
  },
  numFormat3(num, digits = 2) {
    // 正则表达式：去除末尾多余的 0 和小数点
    var rx = /\.0+$|(\.[0-9]*[1-9])0+$/;

    // 使用 toFixed 固定小数位数，并去除多余的 0
    return parseFloat(num.toFixed(digits)).toString().replace(rx, "$1");
  },
  numFormat2(num, digits = 2) {
    var si = [
      {
        value: 1,
        symbol: "",
      },
      {
        value: 1e3,
        symbol: "K",
      },
      {
        value: 1e6,
        symbol: "M",
      },
      {
        value: 1e9,
        symbol: "B",
      },
    ];
    var rx = /\.0+$|(\.[0-9]*[1-9])0+$/;
    var i;
    for (i = si.length - 1; i > 0; i--) {
      if (num >= si[i].value) {
        break;
      }
    }
    return (num / si[i].value).toFixed(digits).replace(rx, "$1") + si[i].symbol;
  },
  // 选中交易类型
  changeActivePicker(e) {
    let that = this;
    // 没有变动选项
    if (e.currentTarget.id === that.data.activePicker) {
      return;
    }

    // 选中指定模块
    that.setData({
      activePicker: e.currentTarget.id,
      // 上面的交易类型变更后 把查询条件重制
      activeStatus: [0],
      curTransactionType: e.currentTarget.id == 1 ? "redeem" : "",
    });

    // 获取切换后的当前交易数据，如果已有数据则直接显示，否则请求新数据
    const currentData = that.getCurrentTransactionData();
    if (currentData.groupedList.length > 0) {
      // 已有数据，直接显示
      that.setData({
        transactionHistoryList: currentData.groupedList,
        isNoMoreData: currentData.isNoMoreData,
        curTransactionPageNum: currentData.pageNum,
      });
    } else {
      // 没有数据，重置当前类型数据
      that.resetTransactionData();
    }
    // 同步复选框选择项
    this.updateStatusArray();
    this.updateActiveStatusText();

    // 如果是充值获取充值记录列表信息
    if (e.currentTarget.id === 0) {
      that.getTransactionRecords(0);
    }
    // 如果是提现获取提现记录列表信息
    if (e.currentTarget.id === 1) {
      that.getRedeemRecords();
    }
    // 如果是系统奖励，获取系统奖励列表信息
    if (e.currentTarget.id === 2) {
      that.getTransactionRecords(2);
    }
  },

  // guanbi
  offTimerSelect() {
    this.setData({
      showSelectTime: false,
    });
  },
  // 打开时间筛选弹框
  openTimerSelect(e) {
    this.setData({
      showSelectTime: true,
    });
  },
  clickTimeLable(e) {
    this.setData({
      activeTimer: e.detail.value,
    });
  },
  changeActiveTimer(e) {
    let that = this;
    // 选中指定模块
    that.setData({
      showSelectTime: false,
    });

    // 重置当前类型的交易数据
    that.resetTransactionData();
    // 如果是充值获取充值记录列表信息
    if (that.data.activePicker === 0) {
      that.getTransactionRecords(0);
    }
    // 如果是提现获取提现记录列表信息
    if (that.data.activePicker === 1) {
      that.getRedeemRecords();
    }
    // 如果是系统奖励，获取系统奖励列表信息
    if (that.data.activePicker === 2) {
      that.getTransactionRecords(2);
    }
  },

  updateActiveStatusText() {
    const res = [];
    this.data.transactionStatusArray.forEach((item) => {
      item.checked = false;
      this.data.activeStatus.forEach((e) => {
        if (e == item.id) {
          // 更新node
          item.checked = true;
          res.push(item.name);
        }
      });
    });
    // let activeStatusText = res[0] == 'All' ? 'Status' : res[0]
    let activeStatusText = res[0];
    this.setData({
      activeStatusText,
      transactionStatusArray: this.data.transactionStatusArray,
    });
  },
  updateStatusArray() {
    let newStatusArray = [];
    switch (this.data.activePicker) {
      case 0:
        newStatusArray = [
          {
            id: 0,
            name: "All",
          },
          {
            id: 1,
            name: "Successful",
          },
          {
            id: 2,
            name: "Pending",
          },
          {
            id: 3,
            name: "Unsuccessful",
          },
        ];
        break;
      case 1:
        newStatusArray = [
          {
            id: 0,
            name: "All",
          },
          {
            id: 1,
            name: "Pending",
          },
          {
            id: 2,
            name: "Successful",
          },
          {
            id: 3,
            name: "Unsuccessful",
          },
        ];
        break;
      case 2:
        newStatusArray = [
          {
            id: 0,
            name: "All",
          },
        ];
        break;
    }
    this.setData({
      transactionStatusArray: newStatusArray,
    });
  },

  // 选中复选框状态
  clickStatusLable(e) {
    const values = e.detail.value;
    // console.log('选中复选框状态', values)
    // const last = values[values.length-1]

    // let res = []
    // if (last == 0) {
    //   res = [0];
    // } else {
    //   res = values.filter(item => item != 0);
    // }

    this.setData({
      // activeStatus: res
      activeStatus: [values],
    });

    this.updateActiveStatusText();
  },

  offStatusSelect() {
    this.setData({
      showSelectStatus: false,
      // activeStatus: [0]
    });
    this.updateActiveStatusText();
  },
  openStatusSelect(e) {
    this.setData({
      showSelectStatus: true,
    });
  },

  // 选中状态
  changeActiveStatus(e) {
    const currentId = e.currentTarget.id;
    let that = this;
    // 选中指定模块
    this.data.curTransactionPageNum = 1;
    if (!this.data.activeStatus.length) {
      this.setData({
        activeStatus: [0],
      });
      this.updateActiveStatusText();
    }
    that.setData({
      showSelectStatus: false,
    });

    // 重置当前类型的交易数据
    that.resetTransactionData();
    this.updateActiveStatusText();
    // 如果是充值获取充值记录列表信息
    if (that.data.activePicker === 0) {
      that.getTransactionRecords(0);
    }
    // 如果是提现获取提现记录列表信息
    if (that.data.activePicker === 1) {
      that.getRedeemRecords();
    }
    // 如果是系统奖励，获取系统奖励列表信息
    if (that.data.activePicker === 2) {
      that.getTransactionRecords(2);
    }
  },

  setNotificationText(text) {
    parse(text)
      .then((nodes) => {
        this.setData({
          notificationText: nodes,
        });
      })
      .catch((err) => {
        console.error("Parse html error:", err);
      });
  },
  // 在页面隐藏时清除定时器
  onHide() {
    if (this.marqueeTimer) {
      clearTimeout(this.marqueeTimer);
    }
  },
  // 在页面卸载时清除定时器
  onUnload() {
    if (this.marqueeTimer) {
      clearTimeout(this.marqueeTimer);
    }
  },
  calculateMarqueeSpeed() {
    const query = my.createSelectorQuery();
    query.select(".notif-message").boundingClientRect();
    query.exec((res) => {
      if (!res || !res[0]) return;

      const messageWidth = res[0].width;
      const speedPxPerSecond = 50; // 50px/秒的速度
      const durationInSeconds = messageWidth / speedPxPerSecond;

      // 设置动画时长到 data 中
      this.setData({
        marqueeDuration: `${durationInSeconds}s`,
      });
    });
  },
  openTerms() {
    my.navigateTo({
      url: "/pages/terms/terms",
    });
  },
  reloading() {
    // 重新进入小程序
    console.log("reloading");
    my.reLaunch({
      url: "/pages/game/list",
    });
  },
});
