import {
  httpClient,
  wwwUrl
} from "/utils/http";
Component({
  mixins: [],
  data: {
    showOpenLinkDialog: false,
  },
  props: {
    token: String,
    jumpTypeInfo: Object,
    showJumpConfrim_1: <PERSON><PERSON>an,
    onClose: Function,
  },
  didMount() {
    this.setData({
      showOpenLinkDialog: this.props.showJumpConfrim_1,
    });
  },
  didUpdate() {
    this.setData({
      showOpenLinkDialog: this.props.showJumpConfrim_1,
    });
  },
  didUnmount() {},
  methods: {
    cancelJumpConfrim_1_done() {
      // this.setData({
      //   showOpenLinkDialog: false
      // })
      // 修改props
      this.props.onClose();
    },
    // 打开外部链接
    openParentLink() {
      my.call("navigateToLink", {
        targetLink: this.props.jumpTypeInfo.url,
        appId: "2170020216334562",
        success: (res) => {
          // my.alert({ content: JSON.stringify(res) });
        },
        fail: (res) => {
          my.alert({
            content: JSON.stringify(res),
          });
        },
      });
    },
    // 打开H5链接
    async confirmJumpWithType() {
      if (this.props.jumpTypeInfo.type == "parent") {
        this.openParentLink();
        return;
      }

      console.log("打开H5链接", this.props);

      try {
        const response = await httpClient.requestWithToken(
          "api/player/login/gcash_token",
          this.props.token || this.props.jumpTypeInfo.token, {}, {
            baseUrl: "common",
            showLoading: true,
          }
        );

        console.log(response);

        // this.tokenInvalid(response.data.code)
        if (response.data.code != 200) {
          my.showToast({
            type: "fail",
            content: response.data.msg || "fail to load redeem record",
            duration: 2000,
          });
          return;
        }

        const gcash_token = response.data.data;
        const targetLink = this.props.jumpTypeInfo.type === 'external' ? this.props.jumpTypeInfo.url :
          wwwUrl +
          "?gcash_auth_code=" +
          gcash_token +
          "&type=" +
          this.props.jumpTypeInfo.type +
          (this.props.jumpTypeInfo.id && this.props.jumpTypeInfo.type !== "promos" ? "&id=" + this.props.jumpTypeInfo.id : "");

        console.log("targetLink", targetLink);

        my.call("navigateToLink", {
          targetLink: targetLink,
          appId: "2170020216334562",
          success: (res) => {
            this.setData({
              showOpenLinkDialog: false,
              showJumpConfrim_1: false,
            });
          },
          fail: (res) => {
            my.alert({
              content: JSON.stringify(res),
            });
          },
        });
      } catch (error) {
        console.error("获取 gcash_token 失败:", error);
        my.showToast({
          type: "fail",
          content: (error.errorMessage && error.errorMessage.msg) || "fail to load redeem record",
          duration: 2000,
        });
        // this.tokenInvalid(error.status);
      }
    },
  },
});