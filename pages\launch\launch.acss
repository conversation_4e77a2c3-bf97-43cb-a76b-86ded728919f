page {
  height: 100%;
  width: 100vw;
}
.launch-page {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.launch-container {
  /* 添加必要的样式 */
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
.launch-logo {
  width: 100%;
  display: flex;
  justify-content: center;
  margin-top: 300rpx;
}
.launch-logo > image {
  width: 286rpx;
  margin: 0 auto;
}

.launch-nav {
  /* 添加必要的样式 */
  width: 80%;
  margin: 0 auto;
  margin-top: auto;
}
.launch-nav > image {
  width: 100%;
}

.loading-box {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  text-align: center;
  margin-top: 5%;
  margin-bottom: 10%;
  width: 90%;
}

.loading-font {
  text-align: center;
  margin-top: 20rpx;
  font-size: 28rpx;
  color: #8D8D8D;
}

.launch-version {
  text-align: center;
  margin-top: 20rpx;
  font-size: 26rpx;
  color: #8D8D8D;
  margin-bottom: 8%;
}
/* 弹窗 */
.mask-one {
  position: fixed;
  width: 100%;
  height: 100%;
  left: 0;
  right: 0;
  background: #00000090;
  display: none;
  z-index: 3;
}

.messageConfrim {
  z-index: 4;
  background: #FFF;
  border-radius: 30rpx;
  width: calc(100% - 60rpx);
  box-sizing: border-box;
  padding: 32rpx;
  margin-bottom: 100rpx;
  padding-bottom: 44rpx;
}

.confrim-title {
  color: #000;
  font-size: 44rpx;
  font-weight: 500;
  text-align: center;
  margin-top: 20rpx;
}

.confrim-title > image {
  width: 48rpx;
  height: 48rpx;
}

.dialog-title-top {
  margin-top: 32rpx;
}

.small-font {
  font-size: 36rpx;
  padding: 20rpx;
  text-align: center;
}

.confrim-param {
  text-align: center;
  font-size: 30rpx;
  color: #707070;
  margin: 32rpx;
  line-height: 44rpx;
}

.confrim-done {
  width: 100%;
  height: 96rpx;
  line-height: 96rpx;
  background: #202635;
  color: #FECE7F;
  font-weight: 500;
  font-size: 36rpx;
  text-align: center;
  border-radius: 48px;
  margin-top: 52rpx;
}

.flexcenter {
  display: flex;
  justify-content: center;
  align-items: center;
}

.flex-zindex {
  z-index: 9999;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
}

.hiddenno {
  display: none;
}

.imagesize{
display:flex;
height: 100%;
width: 100%;
justify-content: center;
align-items:center;
} 

.loading-back {
  margin: 0 auto;
  background: #ccc;
  position: relative;
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
}
.loading-show {
  position: absolute;
  background: #FFF;
  text-align: center;
  line-height: 114rpx;
  width: 114rpx;
  height: 114rpx;
  border-radius: 50%;
  left: 3rpx;
  top: 3rpx;
}

.confirm-flexs {
  display: flex;
  justify-content: space-between;
  gap: 30rpx;

}

.confirm-cancel {
  background: #FFF;
  color: #000;
  border: 2rpx solid #ddd;
}

.activity-pop-content {
  width: 100%;
  position: fixed;
  top: 160rpx;
  z-index: 3;
  text-align: center;
}

.flex {
  display: flex;
}

.progress-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.progress-circle {
  position: relative;
  width: 100px;
  height: 100px;
  border-radius: 50%;
  background: conic-gradient(#333 0deg, #C89C53 0deg);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  font-weight: bold;
  color: #333;
}

.progress-circle::before {
  content: "";
  position: absolute;
  width: 80px;
  height: 80px;
  background-color: white;
  border-radius: 50%;
}

.progress-text {
  position: absolute;
  z-index: 10;
}

.loading-text {
  margin-top: 10px;
  font-size: 14px;
  color: #C89C53;
  font-weight: bold;
}

/* 进度动画 */
@keyframes fillProgress {
  from {
    --progress: 0deg;
  }
  to {
    --progress: 360deg;
  }
}

.animate-progress {
  animation: fillProgress 2s linear forwards;
}

.loading-progress {
  width: 80%;
  height: 20rpx;
  background-color: #E1E1E1;
  border-radius: 10rpx;
  overflow: hidden;
  margin: 20rpx auto;
}

.loading-progress-bar {
  height: 100%;
  width: 0; /* 初始宽度为0 */
  background-color: #AC1140;
  transition: width 0.3s ease; /* 添加过渡效果 */
}