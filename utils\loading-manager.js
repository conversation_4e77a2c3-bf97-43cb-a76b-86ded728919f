/**
 * Loading 状态管理器
 * 解决 my.hideLoading() 未执行的问题
 */
class LoadingManager {
  constructor() {
    // 记录当前 loading 状态
    this.loadingStack = [];
    this.isLoading = false;
    
    // 超时保护，防止 loading 一直显示
    this.timeoutId = null;
    this.maxLoadingTime = 30000; // 30秒超时
  }

  /**
   * 显示 loading
   * @param {Object} options - loading 配置
   * @param {string} options.content - loading 文本
   * @param {number} options.timeout - 超时时间（毫秒）
   * @param {string} options.id - loading 标识符，用于匹配 hide
   */
  show(options = {}) {
    const {
      content = "Loading...",
      timeout = this.maxLoadingTime,
      id = this._generateId()
    } = options;

    try {
      // 如果已经有 loading 在显示，先隐藏
      if (this.isLoading) {
        console.warn('Loading already shown, hiding previous loading');
        this._forceHide();
      }

      // 显示 loading
      my.showLoading({ content });
      
      // 记录状态
      this.isLoading = true;
      this.loadingStack.push({
        id,
        timestamp: Date.now(),
        content
      });

      // 设置超时保护
      this._setTimeoutProtection(timeout, id);

      console.log(`Loading shown with id: ${id}`);
      return id;
    } catch (error) {
      console.error('Failed to show loading:', error);
      return null;
    }
  }

  /**
   * 隐藏 loading
   * @param {string} id - loading 标识符
   */
  hide(id = null) {
    try {
      if (!this.isLoading) {
        console.warn('No loading to hide');
        return false;
      }

      // 如果指定了 id，检查是否匹配
      if (id) {
        const currentLoading = this.loadingStack[this.loadingStack.length - 1];
        if (currentLoading && currentLoading.id !== id) {
          console.warn(`Loading id mismatch. Expected: ${id}, Current: ${currentLoading.id}`);
          // 仍然尝试隐藏，但记录警告
        }
      }

      // 隐藏 loading
      my.hideLoading();
      
      // 更新状态
      this.isLoading = false;
      if (id) {
        this.loadingStack = this.loadingStack.filter(item => item.id !== id);
      } else {
        this.loadingStack.pop();
      }

      // 清除超时保护
      this._clearTimeoutProtection();

      console.log(`Loading hidden with id: ${id || 'latest'}`);
      return true;
    } catch (error) {
      console.error('Failed to hide loading:', error);
      this._forceHide();
      return false;
    }
  }

  /**
   * 强制隐藏所有 loading
   */
  hideAll() {
    try {
      if (this.isLoading) {
        my.hideLoading();
      }
      this.isLoading = false;
      this.loadingStack = [];
      this._clearTimeoutProtection();
      console.log('All loading hidden');
    } catch (error) {
      console.error('Failed to hide all loading:', error);
      this._forceHide();
    }
  }

  /**
   * 检查当前是否有 loading 显示
   */
  isShowing() {
    return this.isLoading;
  }

  /**
   * 获取当前 loading 信息
   */
  getCurrentLoading() {
    if (this.loadingStack.length > 0) {
      return this.loadingStack[this.loadingStack.length - 1];
    }
    return null;
  }

  /**
   * 安全的请求包装器
   * @param {Function} requestFn - 请求函数
   * @param {Object} options - 配置选项
   */
  async wrapRequest(requestFn, options = {}) {
    const {
      showLoading = true,
      loadingContent = "Loading...",
      timeout = this.maxLoadingTime
    } = options;

    let loadingId = null;

    try {
      // 显示 loading
      if (showLoading) {
        loadingId = this.show({
          content: loadingContent,
          timeout
        });
      }

      // 执行请求
      const result = await requestFn();
      
      // 隐藏 loading
      if (loadingId) {
        this.hide(loadingId);
      }

      return result;
    } catch (error) {
      // 确保在错误情况下也隐藏 loading
      if (loadingId) {
        this.hide(loadingId);
      }
      throw error;
    }
  }

  /**
   * 生成唯一 ID
   * @private
   */
  _generateId() {
    return `loading_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 设置超时保护
   * @private
   */
  _setTimeoutProtection(timeout, id) {
    this._clearTimeoutProtection();
    
    this.timeoutId = setTimeout(() => {
      console.warn(`Loading timeout after ${timeout}ms, force hiding. ID: ${id}`);
      this.hide(id);
    }, timeout);
  }

  /**
   * 清除超时保护
   * @private
   */
  _clearTimeoutProtection() {
    if (this.timeoutId) {
      clearTimeout(this.timeoutId);
      this.timeoutId = null;
    }
  }

  /**
   * 强制隐藏（内部使用）
   * @private
   */
  _forceHide() {
    try {
      my.hideLoading();
    } catch (error) {
      console.error('Force hide loading failed:', error);
    }
    this.isLoading = false;
    this.loadingStack = [];
    this._clearTimeoutProtection();
  }
}

// 创建单例实例
const loadingManager = new LoadingManager();

// 导出实例和便捷方法
export default loadingManager;

export const showLoading = (options) => loadingManager.show(options);
export const hideLoading = (id) => loadingManager.hide(id);
export const hideAllLoading = () => loadingManager.hideAll();
export const isLoadingShowing = () => loadingManager.isShowing();
export const wrapRequest = (requestFn, options) => loadingManager.wrapRequest(requestFn, options);
