import {
  httpClient
} from "/utils/http";
import {
  AWARD_NAME_OBJECTS
} from "/utils/types";
// 弹窗管理器类 - 纯逻辑，不涉及UI
export default class PopupManager {
  constructor(info) {
    this.popupQueue = [];
    this.currentPopup = null;
    this.isShowing = false;
    this.token = info.token;
    this.isVip = info.isVip;
    this.register_award = info.register_award;
    this.is_register = info.is_register;
    this.awardTypes = info.awardTypes || [];
    this.popupChangeCallback = null; // 回调函数，通知外部弹窗变化
  }

  // 设置弹窗变化回调函数
  setPopupChangeCallback(callback) {
    this.popupChangeCallback = callback;
  }
  // 设置跳转链接回调
  setOpenLinkCallback(callback) {
    this.openLinkCallback = callback;
  }
  // 初始化弹窗队列
  async initialize() {
    // 1. 检查本地弹窗条件
    await this.checkLocalPopups();

    // 2. 获取服务端活动弹窗
    await this.fetchServerPopups();

    // 3. 排序弹窗队列
    this.sortPopupQueue();

    // a.调账之后的玩家奖励记录弹窗
    await this.getBonusPopups();

    // 4. 开始显示弹窗
    this.showNextPopup();
  }
  // 获取调账后奖励弹窗
  async getBonusPopups() {
    if (!this.token) {
      console.log("未登录，跳过获取服务端弹窗");
      return;
    }
    try {
      const response = await httpClient.get("open/api/activity/adjustment_bonus_record/list", {
        baseUrl: "main",
        token: this.token,
      });
      // console.log("调账之后的玩家奖励记录弹窗getBonusPopups", response)
      const bonusPopups = response.data && response.data.data.list || [];
      // const bonusPopups = [{
      //   "id": "86584224571682824", // 奖励记录id
      //   "activity_name": "Casino Elite Wealth Leaderboard", //副标题
      //   "bonus": "88.33", //金额，单位：元
      //   "bonus_date": "2025/05/07" //奖励日期
      // }, {
      //   "id": "86584224571682824", // 奖励记录id
      //   "activity_name": "Late Night Cashback", //副标题
      //   "bonus": "188.33", //金额，单位：元
      //   "bonus_date": "2025/05/07" //奖励日期
      // }]
      if (bonusPopups.length > 0) {
        for (let bonus of bonusPopups) {
          bonus.activity_name = this.formatStatusName(bonus.activity_name);
          this.addPopup({
            id: "BonusPopups",
            name: "BonusPopups",
            type: "bonus_popups",
            priority: 10,
            data: bonus,
          });
        }
      }
    } catch (error) {
      console.log("获取调账后奖励弹窗 error", error);
    }
  }

  // 检查本地弹窗条件，本地检查的弹窗确认按钮都是跳转链接，返回promise
  async checkLocalPopups() {
    // 检查年龄验证
    const needAgeVerification = await this.checkAgeVerification();
    if (needAgeVerification) {
      this.addPopup({
        id: "Tip21Old",
        name: "Tip21Old",
        type: "age_verification",
        priority: 13,
        data: null,
      });
    }

    // 检查VIP状态 (如果不是VIP，则不检查-- 每次都检查是否是vip，如果是vip则走判断逻辑)
    const needVipPromotion = this.isVip && (await this.checkVipStatus());
    if (needVipPromotion) {
      this.addPopup({
        id: "VipTip",
        name: "VipTip",
        type: "Bet",
        priority: 12,
        data: {
          type: "VipTip",
          id: "123",
        },
      });
    }
    // 检查注册奖励
    const needRegisterBonus = await this.checkRegisterBonus();
    if (needRegisterBonus) {
      this.addPopup({
        id: "RegisterBonus",
        name: "Register Bonus",
        type: "Bet",
        priority: 5,
        data: {
          type: "RegisterBonus",
          amount: this.register_award.amount,
        },
      });
    }
    // 遍历
    // console.log("检查本地弹窗条件====",needAgeVerification,needVipPromotion,needRegisterBonus)
  }
  // 检查21+弹窗
  async checkAgeVerification() {
    // 注册时弹出
    if (this.is_register != 0) {
      return true;
    }
    return false;
  }
  // 检查VIP状态
  async checkVipStatus() {
    // --debug 重置VIP状态检查
    // await this.resetVipStatusChecked();
    // VIP状态每月1-15号检查一次，16-30号检查一次，弹过就不弹
    const now = new Date();
    const month = now.getMonth() + 1;
    const day = now.getDate();
    // 检查当前周期是否弹过
    const {
      data: hasUpChecked
    } =
    (await my.getStorage({
      key: "vip_status_checked_up",
    })) || {};
    // 检查当前周期是否弹过
    const {
      data: hasDownChecked
    } =
    (await my.getStorage({
      key: "vip_status_checked_down",
    })) || {};
    console.log("hasUpChecked", hasUpChecked);
    console.log("hasDownChecked", hasDownChecked);
    // 1-15号并且没有弹过
    if (day >= 1 && day <= 15 && !hasUpChecked) {
      // 设置当前周期已弹过
      my.setStorage({
        key: "vip_status_checked_up",
        data: true,
      });
      // 重置16-30号
      my.setStorage({
        key: "vip_status_checked_down",
        data: false,
      });
      return true;
    }
    // 16-30号并且没有弹过
    if (day >= 16 && day <= 31 && !hasDownChecked) {
      // 设置当前周期已弹过
      my.setStorage({
        key: "vip_status_checked_down",
        data: true,
      });
      // 重置1-15号
      my.setStorage({
        key: "vip_status_checked_up",
        data: false,
      });
      return true;
    }
    return false;
  }
  // 检查注册奖励
  async checkRegisterBonus() {
    const rd = this.register_award;
    if (rd) {
      this.register_award = rd;
      if ([1, 2, 3].includes(rd.type) && +rd.amount > 0) {
        return true;
      }
      return false;
    }
    return false;
  }

  formatStatusName(status_name) {
    if (!status_name) return "";
    return status_name
      .replace(/cashback/gi, (match) => (match[0] === "C" ? "Rebate" : "rebate"))
      .replace(/payday/gi, "Bonus Time")
      .replace(/casino/gi, "Live Table");
  }

  // 获取服务端活动弹窗， 服务端返回的活动弹窗都是领取奖励，需要请求接口， 返回promise
  async fetchServerPopups() {
    if (!this.token) {
      console.log("未登录，跳过获取服务端弹窗");
      return;
    }

    try {
      const response = await httpClient.post(
        "open/api/activity/bonus_list", {
          token: this.token,
        }, {
          baseUrl: "main",
        }
      );

      const activityPopups = response.data && response.data.data && response.data.data.list || [];

      // const activityPopups = [{
      //   "id": "86952019906007045",
      //   "type": 244,
      //   "bonus": 64,
      //   "bonus_date": "2025-05-08",
      //   "jili_back_coin": "160.00"
      // }, {
      //   "id": "86952019906007045",
      //   "type": 246,
      //   "bonus": 164,
      //   "bonus_date": "2025-05-08",
      //   "jili_back_coin": "16.00"
      // }, {
      //   "id": "86952019906007045",
      //   "type": 100001,
      //   "bonus": 104,
      //   "bonus_date": "2025-05-08",
      //   "jili_back_coin": "10.00"
      // }]

      // 处理活动奖励弹窗
      if (activityPopups.length > 0) {
        for (const firstItem of activityPopups) {
          //  只有活动列表第一项为反水活动， 这两个反水活动特殊判断，其他的活动弹窗有什么显示什么，
          // 注意：确认按钮为领取，点击后需要调用接口，然后关闭弹窗并检查显示下一个activityPopups
          let type_name = AWARD_NAME_OBJECTS[firstItem.type] || "";
          let priority = 4; // 优先级
          // 普通反水
          if (firstItem.type === 111) {
            priority = 10;
            // 合规性检查： Cashback 改 Rebate
            type_name = "Daily Rebate";
          }
          // VIP反水
          if ([119, 241].includes(firstItem.type)) {
            priority = 11;
            // 合规性检查： Cashback 改 Rebate
            type_name = "Vip Rebate";
          }
          // 读取后台返回的配置内容
          if (this.awardTypes.length > 0) {
            let indx = this.awardTypes.findIndex((t) => t.change_type == firstItem.type);
            if (indx >= 0) {
              type_name = this.formatStatusName(this.awardTypes[indx].title);
            }
          }
          if (type_name) {
            this.addPopup({
              id: "ActivityBonus",
              name: type_name,
              type: type_name || "ActivityBonus",
              priority: priority,
              data: firstItem,
            });
          }
        }
      }
    } catch (error) {
      console.error("获取活动弹窗失败:", error);
    }
  }
  async resetVipStatusChecked() {
    my.setStorage({
      key: "vip_status_checked_up",
      data: false,
    });
    my.setStorage({
      key: "vip_status_checked_down",
      data: false,
    });
  }

  // 添加弹窗到队列
  addPopup(popup) {
    this.popupQueue.push(popup);
  }

  // 按优先级排序弹窗队列 (数字越大优先级越高)
  sortPopupQueue() {
    this.popupQueue.sort((a, b) => b.priority - a.priority);
  }

  // 显示下一个弹窗
  showNextPopup() {
    if (this.isShowing || this.popupQueue.length === 0) {
      // 如果没有下一个弹窗，通知外部清除当前弹窗
      if (this.popupChangeCallback) {
        this.popupChangeCallback(null);
      }
      return false;
    }
    this.currentPopup = this.popupQueue.shift();
    this.isShowing = true;

    // 通知外部显示弹窗
    if (this.popupChangeCallback) {
      this.popupChangeCallback(this.currentPopup);
    }

    return true;
  }

  // 关闭当前弹窗
  async closeCurrentPopup(type) {
    // 根据不同的弹窗类型处理结果
    this.handlePopupResult(this.currentPopup, type);

    this.isShowing = false;
    this.currentPopup = null;

    // 检查是否有下一个弹窗
    return this.showNextPopup();
  }

  // 调账后奖励弹窗领取确认
  async onConfirmGetBonusPopup(popup) {
    try {
      const response = await httpClient.post(
        "open/api/activity/adjustment_bonus_record/receive", {
          id: popup.data.id,
          token: this.token,
        }, {
          baseUrl: "main",
          token: this.token,
        }
      );
      console.log("调账后奖励弹窗领取确认response", response);
    } catch (error) {
      console.log("调账后奖励弹窗领取确认error", error);
    }
  }

  // 处理弹窗结果
  async handlePopupResult(popup, {
    type
  }) {
    if (!popup) return;
    console.log("处理弹窗结果 result", popup, type);
    // 根据不同的弹窗类型处理结果
    if (type === "close") {
      // 关闭弹窗
    }
    if (type === "open") {
      // 打开弹窗
      // this.openURL(popup);
    }
    if (type === "claim") {
      if (popup.name === "BonusPopups") {
        const res1 = await this.onConfirmGetBonusPopup(popup);
        console.log("领取调账奖励结果", res1);
        return;
      }
      // 领取奖励
      const res = await this.claimAward(popup);
      console.log("领取奖励结果", res);
    }
    // switch (popup.id) {
    //   case 'Tip21Old':
    //     // 处理年龄验证结果
    //     console.log('年龄验证结果:', type);
    //     break;
    //   case 'VipTip':
    //     // 处理VIP提示结果
    //     console.log('VIP提示结果:', type);
    //     break;
    //   case 'VipCashback':
    //     // 处理VIP返现结果
    //     console.log('VIP返现结果:', type);
    //     break;
    //   case 'ActivityBonus':
    //     // 处理活动奖励结果
    //     console.log('活动奖励结果:', type);
    //     break;
    //   case 'RegisterBonus':
    //     // 处理注册奖励结果
    //     console.log('注册奖励结果:', type);
    //     break;
    // }
  }
  // 领取奖励
  async claimAward(popup) {
    const res = await httpClient.post(
      "open/api/activity/bonus_receive", {
        token: this.token,
        id: popup.data.id,
        type: popup.data.type,
      }, {
        baseUrl: "main",
      }
    );
    console.log("领取奖励结果", res);
  }
  // 打开弹窗
  openURL(popup) {
    console.log("打开弹窗", popup);
  }
  // 获取当前弹窗信息
  getCurrentPopup() {
    return this.currentPopup;
  }

  // 检查是否有正在显示的弹窗
  hasActivePopup() {
    return this.isShowing;
  }
}